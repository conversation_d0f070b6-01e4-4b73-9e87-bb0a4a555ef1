// Package database 提供資料庫連線管理功能
// 使用 pgx v5 原生驅動程式與 PostgreSQL 通訊
// 支援連線池、健康檢查、交易管理和類型安全查詢
package database

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	
	"github.com/koopa0/pms-api-v2/internal/platform/config"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// DB 封裝資料庫連線
// 提供類型安全的資料庫操作和連線池管理
type DB struct {
	pool    *pgxpool.Pool
	Queries *sqlc.Queries
}

// Pool returns the underlying connection pool
// This is useful for advanced operations and testing
func (db *DB) Pool() *pgxpool.Pool {
	return db.pool
}

// New 建立新的資料庫連線池
// 使用 pgx v5 原生驅動程式和連線池管理
// 返回已測試可用的資料庫連線池
func New(cfg *config.DatabaseConfig) (*DB, error) {
	// 設定 pgxpool 配置（遵循 Architecture.md 中的 PostgreSQL 17+ 最佳實踐）
	poolConfig, err := pgxpool.ParseConfig(cfg.URL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database config: %w", err)
	}

	// 設定連線池參數
	poolConfig.MaxConns = int32(cfg.MaxConnections) // 基於 CPU 核心數
	poolConfig.MinConns = int32(cfg.MaxIdleConns)   // 保持最小連線數
	poolConfig.MaxConnLifetime = cfg.MaxLifetime    // 連線輪換
	poolConfig.MaxConnIdleTime = 15 * time.Minute   // 空閒連線超時
	poolConfig.HealthCheckPeriod = time.Minute      // 健康檢查週期

	// 創建連線池
	pool, err := pgxpool.NewWithConfig(context.Background(), poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	// 測試資料庫連線
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err = pool.Ping(ctx); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return &DB{
		pool:    pool,
		Queries: sqlc.New(pool),
	}, nil
}

// Close 關閉資料庫連線池
// 應該在應用程式結束時呼叫
func (db *DB) Close() {
	db.pool.Close()
}

// Health 檢查資料庫健康狀態
// 用於健康檢查端點，確保資料庫連線正常
// 會執行 ping 和簡單查詢來驗證連線
func (db *DB) Health(ctx context.Context) error {
	ctx, cancel := context.WithTimeout(ctx, 1*time.Second)
	defer cancel()

	if err := db.pool.Ping(ctx); err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	// 檢查是否可以執行簡單查詢
	var result int
	if err := db.pool.QueryRow(ctx, "SELECT 1").Scan(&result); err != nil {
		return fmt.Errorf("database query check failed: %w", err)
	}

	return nil
}

// BeginTx 開始一個新的交易
// 返回 pgx.Tx 用於類型安全的交易操作
func (db *DB) BeginTx(ctx context.Context) (pgx.Tx, error) {
	return db.pool.Begin(ctx)
}

// Stats 返回連線池統計資訊
// 用於監控和調試連線池狀態
func (db *DB) Stats() *pgxpool.Stat {
	return db.pool.Stat()
}

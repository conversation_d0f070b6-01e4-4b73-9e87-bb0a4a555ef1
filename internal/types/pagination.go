package types

import (
	"math"
)

// PaginationParams represents pagination parameters for list operations
type PaginationParams struct {
	Page     int `json:"page" validate:"min=1"`
	PageSize int `json:"page_size" validate:"min=1,max=100"`
}

// NewPaginationParams creates a new PaginationParams with validation
func NewPaginationParams(page, pageSize int) PaginationParams {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 20 // Default page size
	}
	if pageSize > 100 {
		pageSize = 100 // Maximum page size
	}

	return PaginationParams{
		Page:     page,
		PageSize: pageSize,
	}
}

// Offset calculates the database offset for the pagination
func (p PaginationParams) Offset() int {
	return (p.Page - 1) * p.PageSize
}

// Limit returns the page size limit
func (p PaginationParams) Limit() int {
	return p.PageSize
}

// PaginatedResponse represents a paginated response structure
type PaginatedResponse[T any] struct {
	Data       []T            `json:"data"`
	Pagination PaginationMeta `json:"pagination"`
	Success    bool           `json:"success"`
}

// PaginationMeta contains metadata about pagination
type PaginationMeta struct {
	Page        int  `json:"page"`
	PageSize    int  `json:"page_size"`
	Total       int  `json:"total"`
	TotalPages  int  `json:"total_pages"`
	HasNext     bool `json:"has_next"`
	HasPrevious bool `json:"has_previous"`
}

// NewPaginatedResponse creates a new paginated response
func NewPaginatedResponse[T any](data []T, params PaginationParams, total int) PaginatedResponse[T] {
	totalPages := int(math.Ceil(float64(total) / float64(params.PageSize)))

	meta := PaginationMeta{
		Page:        params.Page,
		PageSize:    params.PageSize,
		Total:       total,
		TotalPages:  totalPages,
		HasNext:     params.Page < totalPages,
		HasPrevious: params.Page > 1,
	}

	return PaginatedResponse[T]{
		Data:       data,
		Pagination: meta,
		Success:    true,
	}
}

// EmptyPaginatedResponse creates an empty paginated response
func EmptyPaginatedResponse[T any](params PaginationParams) PaginatedResponse[T] {
	return NewPaginatedResponse([]T{}, params, 0)
}

// PaginationRequest represents a request with pagination parameters
type PaginationRequest struct {
	PaginationParams
}

// Validate validates the pagination parameters
func (p *PaginationParams) Validate() error {
	if p.Page < 1 {
		return &ValidationError{Field: "page", Message: "must be greater than 0"}
	}
	if p.PageSize < 1 {
		return &ValidationError{Field: "page_size", Message: "must be greater than 0"}
	}
	if p.PageSize > 100 {
		return &ValidationError{Field: "page_size", Message: "must be less than or equal to 100"}
	}
	return nil
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// Error implements the error interface
func (e *ValidationError) Error() string {
	return e.Field + ": " + e.Message
}

// PaginationConfig holds default pagination configuration
type PaginationConfig struct {
	DefaultPageSize int
	MaxPageSize     int
}

// DefaultPaginationConfig returns the default pagination configuration
func DefaultPaginationConfig() PaginationConfig {
	return PaginationConfig{
		DefaultPageSize: 20,
		MaxPageSize:     100,
	}
}

// ApplyDefaults applies default values to pagination parameters
func (p *PaginationParams) ApplyDefaults(config PaginationConfig) {
	if p.Page < 1 {
		p.Page = 1
	}
	if p.PageSize < 1 {
		p.PageSize = config.DefaultPageSize
	}
	if p.PageSize > config.MaxPageSize {
		p.PageSize = config.MaxPageSize
	}
}

// CalculatePages calculates pagination information
func CalculatePages(total, pageSize int) (totalPages int, hasMore bool) {
	if pageSize <= 0 {
		return 0, false
	}

	totalPages = int(math.Ceil(float64(total) / float64(pageSize)))
	hasMore = total > pageSize

	return totalPages, hasMore
}

// GetPageRange returns the start and end indices for a page
func (p PaginationParams) GetPageRange() (start, end int) {
	start = p.Offset()
	end = start + p.PageSize
	return start, end
}

// IsValidPage checks if the page number is valid for the given total
func (p PaginationParams) IsValidPage(total int) bool {
	if p.Page < 1 {
		return false
	}

	totalPages := int(math.Ceil(float64(total) / float64(p.PageSize)))
	return p.Page <= totalPages
}

// GetLimitOffset returns the limit and offset for database queries
func (p PaginationParams) GetLimitOffset() (limit int32, offset int32) {
	return int32(p.PageSize), int32(p.Offset())
}

// GetMeta creates pagination metadata based on total count
func (p PaginationParams) GetMeta(total int) PaginationMeta {
	totalPages := int(math.Ceil(float64(total) / float64(p.PageSize)))

	return PaginationMeta{
		Page:        p.Page,
		PageSize:    p.PageSize,
		Total:       total,
		TotalPages:  totalPages,
		HasNext:     p.Page < totalPages,
		HasPrevious: p.Page > 1,
	}
}

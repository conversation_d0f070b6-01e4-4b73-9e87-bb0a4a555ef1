package registration

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"

	"github.com/koopa0/pms-api-v2/internal/business/auth"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// RegistrationServiceInterface defines the interface for registration service
type RegistrationServiceInterface interface {
	Create(ctx context.Context, req *CreateRegistrationRequest) (sqlc.RegistrationRequest, error)
	ApproveRegistration(ctx context.Context, registrationID int32, approverID int32) error
	RejectRegistration(ctx context.Context, registrationID int32, approverID int32, reason string) error
	ListPendingRegistrations(ctx context.Context, offset, limit int32) ([]sqlc.RegistrationRequest, error)
	GetRegistrationByID(ctx context.Context, id int32) (sqlc.RegistrationRequest, error)
}

// MockService implements a mock registration service for testing
type MockService struct {
	createResult                    sqlc.RegistrationRequest
	createError                     error
	approveError                    error
	rejectError                     error
	listPendingRegistrationsResult  []sqlc.RegistrationRequest
	listPendingRegistrationsError   error
	getRegistrationByIDResult       sqlc.RegistrationRequest
	getRegistrationByIDError        error
}

func (m *MockService) Create(ctx context.Context, req *CreateRegistrationRequest) (sqlc.RegistrationRequest, error) {
	if m.createError != nil {
		return sqlc.RegistrationRequest{}, m.createError
	}
	return m.createResult, nil
}

func (m *MockService) ApproveRegistration(ctx context.Context, registrationID int32, approverID int32) error {
	return m.approveError
}

func (m *MockService) RejectRegistration(ctx context.Context, registrationID int32, approverID int32, reason string) error {
	return m.rejectError
}

func (m *MockService) ListPendingRegistrations(ctx context.Context, offset, limit int32) ([]sqlc.RegistrationRequest, error) {
	if m.listPendingRegistrationsError != nil {
		return nil, m.listPendingRegistrationsError
	}
	return m.listPendingRegistrationsResult, nil
}

func (m *MockService) GetRegistrationByID(ctx context.Context, id int32) (sqlc.RegistrationRequest, error) {
	if m.getRegistrationByIDError != nil {
		return sqlc.RegistrationRequest{}, m.getRegistrationByIDError
	}
	return m.getRegistrationByIDResult, nil
}

// TestHandler wraps the handler to work with the interface for testing
type TestHandler struct {
	service RegistrationServiceInterface
}

func (h *TestHandler) Register(w http.ResponseWriter, r *http.Request) {
	var req RegisterRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		response := map[string]interface{}{
			"success": false,
			"error": map[string]interface{}{
				"message": "Invalid request format",
			},
		}
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(response)
		return
	}

	// Convert to service request
	createReq := &CreateRegistrationRequest{
		CompanyName:   req.CompanyName,
		CompanyType:   sqlc.CompanyType(req.CompanyType),
		UniformNumber: req.UniformNumber,
		CompanyOwner:  req.CompanyOwner,
		Address:       req.Address,
		ContactPerson: req.ContactPerson,
		JobTitle:      req.JobTitle,
		ContactPhone:  req.ContactPhone,
		ContactMobile: req.ContactMobile,
		ContactEmail:  req.ContactEmail,
		BackupEmail:   req.BackupEmail,
		Username:      req.Username,
		Password:      req.Password,
	}

	// Create registration
	registration, err := h.service.Create(r.Context(), createReq)
	if err != nil {
		h.handleRegistrationError(r.Context(), w, err)
		return
	}

	// Return success response with 201 status
	responseData := map[string]interface{}{
		"message":         constants.MsgRegistered,
		"registration_id": registration.ID,
		"status":          registration.Status,
		"created_at":      registration.CreatedAt,
	}

	response := map[string]interface{}{
		"success": true,
		"data":    responseData,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(response)
}

func (h *TestHandler) ListPending(w http.ResponseWriter, r *http.Request) {
	// Check authentication
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Check permission - only CISA and SPO can review
	if claims.Role != "CISA" && claims.Role != "SPO" {
		http.Error(w, "Forbidden", http.StatusForbidden)
		return
	}

	// Parse pagination
	page := 1
	perPage := 20

	offset := int32((page - 1) * perPage)
	limit := int32(perPage)

	// Get pending registrations
	registrations, err := h.service.ListPendingRegistrations(r.Context(), offset, limit)
	if err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Format response
	items := make([]map[string]interface{}, len(registrations))
	for i, reg := range registrations {
		items[i] = map[string]interface{}{
			"id":             reg.ID,
			"company_name":   reg.CompanyName,
			"uniform_number": reg.UnifiedBusinessNo,
			"contact_person": reg.ContactPerson,
			"contact_email":  reg.Email,
			"status":         reg.Status,
			"created_at":     reg.CreatedAt,
		}
	}

	response := map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"items":    items,
			"page":     page,
			"per_page": perPage,
			"total":    len(items),
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (h *TestHandler) GetRegistration(w http.ResponseWriter, r *http.Request) {
	// Check authentication
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Check permission
	if claims.Role != "CISA" && claims.Role != "SPO" {
		http.Error(w, "Forbidden", http.StatusForbidden)
		return
	}

	// Get ID from URL
	idStr := r.PathValue("id")
	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	// Get registration
	registration, err := h.service.GetRegistrationByID(r.Context(), int32(id))
	if err != nil {
		http.Error(w, "Not found", http.StatusNotFound)
		return
	}

	response := map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"id":             registration.ID,
			"company_name":   registration.CompanyName,
			"uniform_number": registration.UnifiedBusinessNo,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (h *TestHandler) Approve(w http.ResponseWriter, r *http.Request) {
	// Check authentication
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Check permission
	if claims.Role != "CISA" && claims.Role != "SPO" {
		http.Error(w, "Forbidden", http.StatusForbidden)
		return
	}

	// Get ID from URL
	idStr := r.PathValue("id")
	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	// Approve registration
	err = h.service.ApproveRegistration(r.Context(), int32(id), claims.UserID)
	if err != nil {
		h.handleApprovalError(r.Context(), w, err)
		return
	}

	response := map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"message": constants.MsgRegistrationApproved,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (h *TestHandler) Reject(w http.ResponseWriter, r *http.Request) {
	// Check authentication
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Check permission
	if claims.Role != "CISA" && claims.Role != "SPO" {
		http.Error(w, "Forbidden", http.StatusForbidden)
		return
	}

	// Get ID from URL
	idStr := r.PathValue("id")
	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid ID", http.StatusBadRequest)
		return
	}

	// Parse request body
	var req RejectRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request format", http.StatusBadRequest)
		return
	}

	if req.Reason == "" {
		http.Error(w, "Reason cannot be empty", http.StatusBadRequest)
		return
	}

	// Reject registration
	err = h.service.RejectRegistration(r.Context(), int32(id), claims.UserID, req.Reason)
	if err != nil {
		h.handleApprovalError(r.Context(), w, err)
		return
	}

	response := map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"message": constants.MsgRegistrationRejected,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (h *TestHandler) handleRegistrationError(ctx context.Context, w http.ResponseWriter, err error) {
	var statusCode int
	var message string

	switch {
	case strings.Contains(err.Error(), "已被使用") || strings.Contains(err.Error(), "已註冊") || strings.Contains(err.Error(), "已有待審核"):
		statusCode = http.StatusConflict
		message = err.Error()
	case strings.Contains(err.Error(), "不能為空") || strings.Contains(err.Error(), "長度") || strings.Contains(err.Error(), "無效"):
		statusCode = http.StatusBadRequest
		message = err.Error()
	default:
		statusCode = http.StatusInternalServerError
		message = "Internal server error"
	}

	response := map[string]interface{}{
		"success": false,
		"error": map[string]interface{}{
			"message": message,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

func (h *TestHandler) handleApprovalError(ctx context.Context, w http.ResponseWriter, err error) {
	var statusCode int
	var message string

	switch {
	case strings.Contains(err.Error(), "不存在"):
		statusCode = http.StatusNotFound
		message = err.Error()
	case strings.Contains(err.Error(), "已處理過"):
		statusCode = http.StatusConflict
		message = err.Error()
	case strings.Contains(err.Error(), "不能為空"):
		statusCode = http.StatusBadRequest
		message = err.Error()
	default:
		statusCode = http.StatusInternalServerError
		message = "Internal server error"
	}

	response := map[string]interface{}{
		"success": false,
		"error": map[string]interface{}{
			"message": message,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// Helper function to create test handler with mock service
func createTestHandler() (*TestHandler, *MockService) {
	mockService := &MockService{
		createResult: sqlc.RegistrationRequest{
			ID:                1,
			CompanyName:       "測試公司",
			UnifiedBusinessNo: "12345675",
			CompanyOwner:      "張三",
			ContactPerson:     "李四",
			Email:             "<EMAIL>",
			Status:            constants.RegistrationStatuses.PendingRegistration,
			CreatedAt:         pgtype.Timestamp{Time: time.Now(), Valid: true},
		},
		getRegistrationByIDResult: sqlc.RegistrationRequest{
			ID:                1,
			CompanyName:       "測試公司",
			UnifiedBusinessNo: "12345675",
			CompanyOwner:      "張三",
			ContactPerson:     "李四",
			Email:             "<EMAIL>",
			Status:            constants.RegistrationStatuses.PendingRegistration,
			CreatedAt:         pgtype.Timestamp{Time: time.Now(), Valid: true},
		},
		listPendingRegistrationsResult: []sqlc.RegistrationRequest{
			{
				ID:                1,
				CompanyName:       "公司1",
				UnifiedBusinessNo: "12345675",
				ContactPerson:     "聯絡人1",
				Email:             "<EMAIL>",
				Status:            constants.RegistrationStatuses.PendingRegistration,
			},
			{
				ID:                2,
				CompanyName:       "公司2",
				UnifiedBusinessNo: "87654321",
				ContactPerson:     "聯絡人2",
				Email:             "<EMAIL>",
				Status:            constants.RegistrationStatuses.PendingRegistration,
			},
		},
	}

	// Create a test handler that accepts the interface
	handler := &TestHandler{service: mockService}
	return handler, mockService
}

// Helper function to create authenticated request with claims
func createAuthenticatedRequest(method, url string, body []byte, role string) *http.Request {
	req := httptest.NewRequest(method, url, bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	// Add authentication context
	claims := &auth.Claims{
		UserID:   1,
		Email:    "<EMAIL>",
		Username: "admin",
		Role:     sqlc.UserRole(role),
	}
	ctx := context.WithValue(req.Context(), auth.ContextKeyUser, claims)
	return req.WithContext(ctx)
}

func TestHandler_Register(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		requestBody    interface{}
		setupMock      func(*MockService)
		expectedStatus int
		validateResponse func(*testing.T, map[string]interface{})
	}{
		{
			name: "successful registration",
			requestBody: RegisterRequest{
				CompanyName:   "測試公司",
				CompanyType:   sqlc.CompanyType("軟體廠商"),
				UniformNumber: "12345675",
				CompanyOwner:  "張三",
				ContactPerson: "李四",
				ContactEmail:  "<EMAIL>",
				Username:      "testuser",
				Password:      "password123",
			},
			setupMock: func(ms *MockService) {
				// Default mock is sufficient
			},
			expectedStatus: http.StatusCreated,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.True(t, resp["success"].(bool))
				data := resp["data"].(map[string]interface{})
				assert.Equal(t, constants.MsgRegistered, data["message"])
				assert.Equal(t, float64(1), data["registration_id"])
			},
		},
		{
			name: "invalid JSON body",
			requestBody: "invalid json",
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			expectedStatus: http.StatusBadRequest,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name: "service validation error",
			requestBody: RegisterRequest{
				CompanyName: "", // Missing required field
			},
			setupMock: func(ms *MockService) {
				ms.createError = errors.New("公司名稱不能為空")
			},
			expectedStatus: http.StatusBadRequest,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name: "username already exists error",
			requestBody: RegisterRequest{
				CompanyName:   "測試公司",
				CompanyType:   sqlc.CompanyType("軟體廠商"),
				UniformNumber: "12345675",
				CompanyOwner:  "張三",
				ContactPerson: "李四",
				ContactEmail:  "<EMAIL>",
				Username:      "existinguser",
				Password:      "password123",
			},
			setupMock: func(ms *MockService) {
				ms.createError = errors.New("使用者名稱 existinguser 已被使用，請選擇其他名稱")
			},
			expectedStatus: http.StatusConflict,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				if success, ok := resp["success"].(bool); ok {
					assert.False(t, success)
				} else {
					t.Errorf("Expected success field to be bool, got %T", resp["success"])
				}
			},
		},
		{
			name: "uniform number already exists error",
			requestBody: RegisterRequest{
				CompanyName:   "測試公司",
				CompanyType:   sqlc.CompanyType("軟體廠商"),
				UniformNumber: "12345675",
				CompanyOwner:  "張三",
				ContactPerson: "李四",
				ContactEmail:  "<EMAIL>",
				Username:      "testuser",
				Password:      "password123",
			},
			setupMock: func(ms *MockService) {
				ms.createError = errors.New("統一編號 12345675 已註冊，請確認輸入是否正確")
			},
			expectedStatus: http.StatusConflict,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				if success, ok := resp["success"].(bool); ok {
					assert.False(t, success)
				} else {
					t.Errorf("Expected success field to be bool, got %T", resp["success"])
				}
			},
		},
		{
			name: "internal server error",
			requestBody: RegisterRequest{
				CompanyName:   "測試公司",
				CompanyType:   sqlc.CompanyType("軟體廠商"),
				UniformNumber: "12345675",
				CompanyOwner:  "張三",
				ContactPerson: "李四",
				ContactEmail:  "<EMAIL>",
				Username:      "testuser",
				Password:      "password123",
			},
			setupMock: func(ms *MockService) {
				ms.createError = errors.New("database connection failed")
			},
			expectedStatus: http.StatusInternalServerError,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				if success, ok := resp["success"].(bool); ok {
					assert.False(t, success)
				} else {
					t.Errorf("Expected success field to be bool, got %T", resp["success"])
				}
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			handler, mockService := createTestHandler()
			tt.setupMock(mockService)

			// Create request
			bodyBytes, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/api/v1/register", bytes.NewBuffer(bodyBytes))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// Execute
			handler.Register(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			if err != nil {
				t.Logf("Response body: %s", w.Body.String())
				t.Logf("Response headers: %v", w.Header())
			}
			assert.NoError(t, err)

			tt.validateResponse(t, response)
		})
	}
}

func TestHandler_ListPending(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupAuth      func(*http.Request)
		setupMock      func(*MockService)
		queryParams    string
		expectedStatus int
		validateResponse func(*testing.T, map[string]interface{})
	}{
		{
			name: "successful list with CISA role",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// Default mock is sufficient
			},
			queryParams:    "?page=1&per_page=20",
			expectedStatus: http.StatusOK,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.True(t, resp["success"].(bool))
				data := resp["data"].(map[string]interface{})
				items := data["items"].([]interface{})
				assert.Len(t, items, 2)
				assert.Equal(t, float64(1), data["page"])
				assert.Equal(t, float64(20), data["per_page"])
			},
		},
		{
			name: "successful list with SPO role",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "spo_user",
					Role:     "SPO",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// Default mock is sufficient
			},
			queryParams:    "",
			expectedStatus: http.StatusOK,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.True(t, resp["success"].(bool))
			},
		},
		{
			name: "unauthorized - no auth context",
			setupAuth: func(r *http.Request) {
				// No auth context
			},
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			queryParams:    "",
			expectedStatus: http.StatusUnauthorized,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name: "forbidden - company role",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "company_user",
					Role:     constants.UserRoles.Company,
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			queryParams:    "",
			expectedStatus: http.StatusForbidden,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name: "database error",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				ms.listPendingRegistrationsError = errors.New("database error")
			},
			queryParams:    "",
			expectedStatus: http.StatusInternalServerError,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			handler, mockService := createTestHandler()
			tt.setupMock(mockService)

			// Create request
			req := httptest.NewRequest(http.MethodGet, "/api/v1/registrations/pending"+tt.queryParams, nil)
			tt.setupAuth(req)
			w := httptest.NewRecorder()

			// Execute
			handler.ListPending(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			tt.validateResponse(t, response)
		})
	}
}

func TestHandler_GetRegistration(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		registrationID string
		setupAuth      func(*http.Request)
		setupMock      func(*MockService)
		expectedStatus int
		validateResponse func(*testing.T, map[string]interface{})
	}{
		{
			name:           "successful get registration",
			registrationID: "1",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// Default mock is sufficient
			},
			expectedStatus: http.StatusOK,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.True(t, resp["success"].(bool))
				data := resp["data"].(map[string]interface{})
				assert.Equal(t, float64(1), data["id"])
				assert.Equal(t, "測試公司", data["company_name"])
			},
		},
		{
			name:           "invalid registration ID",
			registrationID: "invalid",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			expectedStatus: http.StatusBadRequest,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name:           "unauthorized access",
			registrationID: "1",
			setupAuth: func(r *http.Request) {
				// No auth context
			},
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			expectedStatus: http.StatusUnauthorized,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name:           "forbidden - company role",
			registrationID: "1",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "company_user",
					Role:     constants.UserRoles.Company,
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			expectedStatus: http.StatusForbidden,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name:           "registration not found",
			registrationID: "999",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				ms.getRegistrationByIDError = errors.New("registration not found")
			},
			expectedStatus: http.StatusNotFound,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			handler, mockService := createTestHandler()
			tt.setupMock(mockService)

			// Create request with path value
			req := httptest.NewRequest(http.MethodGet, "/api/v1/registrations/"+tt.registrationID, nil)
			req.SetPathValue("id", tt.registrationID)
			tt.setupAuth(req)
			w := httptest.NewRecorder()

			// Execute
			handler.GetRegistration(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			tt.validateResponse(t, response)
		})
	}
}

func TestHandler_Approve(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		registrationID string
		setupAuth      func(*http.Request)
		setupMock      func(*MockService)
		expectedStatus int
		validateResponse func(*testing.T, map[string]interface{})
	}{
		{
			name:           "successful approval",
			registrationID: "1",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// Default mock is sufficient (no error)
			},
			expectedStatus: http.StatusOK,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.True(t, resp["success"].(bool))
				data := resp["data"].(map[string]interface{})
				assert.Equal(t, constants.MsgRegistrationApproved, data["message"])
			},
		},
		{
			name:           "invalid registration ID",
			registrationID: "invalid",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			expectedStatus: http.StatusBadRequest,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name:           "unauthorized access",
			registrationID: "1",
			setupAuth: func(r *http.Request) {
				// No auth context
			},
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			expectedStatus: http.StatusUnauthorized,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name:           "forbidden - company role",
			registrationID: "1",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "company_user",
					Role:     constants.UserRoles.Company,
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			expectedStatus: http.StatusForbidden,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name:           "registration not found",
			registrationID: "999",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				ms.approveError = errors.New("註冊申請不存在")
			},
			expectedStatus: http.StatusNotFound,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name:           "registration already processed",
			registrationID: "1",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				ms.approveError = errors.New("此申請已處理過")
			},
			expectedStatus: http.StatusConflict,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			handler, mockService := createTestHandler()
			tt.setupMock(mockService)

			// Create request with path value
			req := httptest.NewRequest(http.MethodPut, "/api/v1/registrations/"+tt.registrationID+"/approve", nil)
			req.SetPathValue("id", tt.registrationID)
			tt.setupAuth(req)
			w := httptest.NewRecorder()

			// Execute
			handler.Approve(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			tt.validateResponse(t, response)
		})
	}
}

func TestHandler_Reject(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		registrationID string
		requestBody    interface{}
		setupAuth      func(*http.Request)
		setupMock      func(*MockService)
		expectedStatus int
		validateResponse func(*testing.T, map[string]interface{})
	}{
		{
			name:           "successful rejection",
			registrationID: "1",
			requestBody: RejectRequest{
				Reason: "文件不完整",
			},
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// Default mock is sufficient (no error)
			},
			expectedStatus: http.StatusOK,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.True(t, resp["success"].(bool))
				data := resp["data"].(map[string]interface{})
				assert.Equal(t, constants.MsgRegistrationRejected, data["message"])
			},
		},
		{
			name:           "invalid JSON body",
			registrationID: "1",
			requestBody:    "invalid json",
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			expectedStatus: http.StatusBadRequest,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name:           "empty rejection reason",
			registrationID: "1",
			requestBody: RejectRequest{
				Reason: "",
			},
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			expectedStatus: http.StatusBadRequest,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name:           "invalid registration ID",
			registrationID: "invalid",
			requestBody: RejectRequest{
				Reason: "文件不完整",
			},
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			expectedStatus: http.StatusBadRequest,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name:           "unauthorized access",
			registrationID: "1",
			requestBody: RejectRequest{
				Reason: "文件不完整",
			},
			setupAuth: func(r *http.Request) {
				// No auth context
			},
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			expectedStatus: http.StatusUnauthorized,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name:           "forbidden - company role",
			registrationID: "1",
			requestBody: RejectRequest{
				Reason: "文件不完整",
			},
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "company_user",
					Role:     constants.UserRoles.Company,
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				// No setup needed
			},
			expectedStatus: http.StatusForbidden,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
		{
			name:           "registration not found",
			registrationID: "999",
			requestBody: RejectRequest{
				Reason: "文件不完整",
			},
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     "CISA",
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			setupMock: func(ms *MockService) {
				ms.rejectError = errors.New("註冊申請不存在")
			},
			expectedStatus: http.StatusNotFound,
			validateResponse: func(t *testing.T, resp map[string]interface{}) {
				assert.False(t, resp["success"].(bool))
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			handler, mockService := createTestHandler()
			tt.setupMock(mockService)

			// Create request with path value and body
			bodyBytes, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodPut, "/api/v1/registrations/"+tt.registrationID+"/reject", bytes.NewBuffer(bodyBytes))
			req.Header.Set("Content-Type", "application/json")
			req.SetPathValue("id", tt.registrationID)
			tt.setupAuth(req)
			w := httptest.NewRecorder()

			// Execute
			handler.Reject(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			tt.validateResponse(t, response)
		})
	}
}

// Benchmark tests for performance-critical operations
func BenchmarkHandler_Register(b *testing.B) {
	handler, _ := createTestHandler()
	requestBody := RegisterRequest{
		CompanyName:   "測試公司",
		CompanyType:   sqlc.CompanyType("軟體廠商"),
		UniformNumber: "12345675",
		CompanyOwner:  "張三",
		ContactPerson: "李四",
		ContactEmail:  "<EMAIL>",
		Username:      "testuser",
		Password:      "password123",
	}
	bodyBytes, _ := json.Marshal(requestBody)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest(http.MethodPost, "/api/v1/register", bytes.NewBuffer(bodyBytes))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		handler.Register(w, req)
	}
}

func BenchmarkHandler_ListPending(b *testing.B) {
	handler, _ := createTestHandler()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := createAuthenticatedRequest(http.MethodGet, "/api/v1/registrations/pending", nil, "CISA")
		w := httptest.NewRecorder()
		handler.ListPending(w, req)
	}
}

// Fuzzing tests
func FuzzHandler_Register(f *testing.F) {
	// Add seed corpus
	f.Add("測試公司", "軟體廠商", "12345675", "張三", "李四", "<EMAIL>", "testuser", "password123")
	f.Add("", "", "", "", "", "", "", "")
	f.Add("A", "軟體廠商", "1234567", "B", "C", "invalid", "u", "p")

	f.Fuzz(func(t *testing.T, companyName, companyType, uniformNumber, companyOwner, contactPerson, contactEmail, username, password string) {
		handler, _ := createTestHandler()

		requestBody := RegisterRequest{
			CompanyName:   companyName,
			CompanyType:   sqlc.CompanyType(companyType),
			UniformNumber: uniformNumber,
			CompanyOwner:  companyOwner,
			ContactPerson: contactPerson,
			ContactEmail:  contactEmail,
			Username:      username,
			Password:      password,
		}

		bodyBytes, err := json.Marshal(requestBody)
		if err != nil {
			return // Skip invalid JSON
		}

		req := httptest.NewRequest(http.MethodPost, "/api/v1/register", bytes.NewBuffer(bodyBytes))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		// Function should not panic
		handler.Register(w, req)

		// Should return a valid HTTP status code
		assert.True(t, w.Code >= 200 && w.Code < 600)

		// Response should be valid JSON
		var response map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
	})
}

// Edge case tests
func TestHandler_EdgeCases(t *testing.T) {
	t.Parallel()

	t.Run("pagination edge cases", func(t *testing.T) {
		handler, _ := createTestHandler()

		tests := []struct {
			name        string
			queryParams string
			expectPage  float64
			expectSize  float64
		}{
			{"negative page", "?page=-1&per_page=10", 1, 10},
			{"zero page", "?page=0&per_page=10", 1, 10},
			{"negative per_page", "?page=1&per_page=-1", 1, 20},
			{"zero per_page", "?page=1&per_page=0", 1, 20},
			{"excessive per_page", "?page=1&per_page=1000", 1, 100},
			{"non-numeric values", "?page=abc&per_page=xyz", 1, 20},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				req := createAuthenticatedRequest(http.MethodGet, "/api/v1/registrations/pending"+tt.queryParams, nil, "CISA")
				w := httptest.NewRecorder()

				handler.ListPending(w, req)

				assert.Equal(t, http.StatusOK, w.Code)
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				data := response["data"].(map[string]interface{})
				assert.Equal(t, tt.expectPage, data["page"])
				assert.Equal(t, tt.expectSize, data["per_page"])
			})
		}
	})

	t.Run("large registration ID handling", func(t *testing.T) {
		handler, _ := createTestHandler()

		// Test with maximum int32 value
		maxInt32 := strconv.FormatInt(int64(^uint32(0)>>1), 10)
		req := createAuthenticatedRequest(http.MethodGet, "/api/v1/registrations/"+maxInt32, nil, "CISA")
		req.SetPathValue("id", maxInt32)
		w := httptest.NewRecorder()

		handler.GetRegistration(w, req)

		// Should handle large numbers gracefully
		assert.True(t, w.Code >= 200 && w.Code < 600)
	})

	t.Run("empty request body handling", func(t *testing.T) {
		handler, _ := createTestHandler()

		req := httptest.NewRequest(http.MethodPost, "/api/v1/register", bytes.NewBuffer([]byte{}))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.Register(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("malformed JSON handling", func(t *testing.T) {
		handler, _ := createTestHandler()

		req := httptest.NewRequest(http.MethodPost, "/api/v1/register", bytes.NewBuffer([]byte("{")))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.Register(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
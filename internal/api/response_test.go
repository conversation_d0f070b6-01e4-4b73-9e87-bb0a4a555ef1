package api

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/koopa0/pms-api-v2/internal/constants"
)

// TestResponseFormat tests the standardized response format
func TestResponseFormat(t *testing.T) {
	tests := []struct {
		name         string
		setupFunc    func(w http.ResponseWriter, ctx context.Context)
		expectStatus int
		expectError  bool
		expectData   bool
		expectMeta   bool
	}{
		{
			name: "Success response format",
			setupFunc: func(w http.ResponseWriter, ctx context.Context) {
				Success(ctx, w, map[string]string{"message": "test"})
			},
			expectStatus: http.StatusOK,
			expectError:  false,
			expectData:   true,
		},
		{
			name: "Error response format",
			setupFunc: func(w http.ResponseWriter, ctx context.Context) {
				Error(ctx, w, http.StatusBadRequest, ErrCodeBadRequest, "test error")
			},
			expectStatus: http.StatusBadRequest,
			expectError:  true,
			expectData:   false,
		},
		{
			name: "Validation error format",
			setupFunc: func(w http.ResponseWriter, ctx context.Context) {
				details := map[string]any{"field": "error message"}
				ValidationError(ctx, w, "validation failed", details)
			},
			expectStatus: http.StatusBadRequest,
			expectError:  true,
			expectData:   false,
		},
		{
			name: "Success with pagination format",
			setupFunc: func(w http.ResponseWriter, ctx context.Context) {
				meta := &Meta{Page: 1, PageSize: 20, Total: 100, TotalPages: 5}
				SuccessWithMeta(ctx, w, []string{"item1", "item2"}, meta)
			},
			expectStatus: http.StatusOK,
			expectError:  false,
			expectData:   true,
			expectMeta:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a test context with request ID
			ctx := context.WithValue(context.Background(), constants.ContextKeyRequestID, "test-request-123")

			// Create recorder
			w := httptest.NewRecorder()

			// Execute the function
			tt.setupFunc(w, ctx)

			// Check status code
			if w.Code != tt.expectStatus {
				t.Errorf("Expected status %d, got %d", tt.expectStatus, w.Code)
			}

			// Check content type
			contentType := w.Header().Get("Content-Type")
			if contentType != "application/json" {
				t.Errorf("Expected Content-Type application/json, got %s", contentType)
			}

			// Parse response
			var response Response
			if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
				t.Fatalf("Failed to parse response JSON: %v", err)
			}

			// Validate common fields
			if response.Success == tt.expectError {
				t.Errorf("Expected success=%v, got success=%v", !tt.expectError, response.Success)
			}

			// Check timestamp is set
			if response.Timestamp.IsZero() {
				t.Error("Timestamp should be set")
			}

			// Check request ID is set
			if response.RequestID == "" {
				t.Error("RequestID should be set")
			}
			if response.RequestID != "test-request-123" {
				t.Errorf("Expected RequestID test-request-123, got %s", response.RequestID)
			}

			// Check error field
			if tt.expectError {
				if response.Error == nil {
					t.Error("Error field should be set for error responses")
				} else {
					// Validate error structure
					if response.Error.Code == "" {
						t.Error("Error code should be set")
					}
					if response.Error.Message == "" {
						t.Error("Error message should be set")
					}
					if response.Error.Timestamp.IsZero() {
						t.Error("Error timestamp should be set")
					}
					if response.Error.RequestID != response.RequestID {
						t.Error("Error RequestID should match response RequestID")
					}
				}
			} else {
				if response.Error != nil {
					t.Error("Error field should be nil for success responses")
				}
			}

			// Check data field
			if tt.expectData {
				if response.Data == nil {
					t.Error("Data field should be set for success responses")
				}
			} else {
				if response.Data != nil {
					t.Error("Data field should be nil for error responses")
				}
			}

			// Check meta field
			if tt.expectMeta {
				if response.Meta == nil {
					t.Error("Meta field should be set when expected")
				} else {
					// Validate meta structure
					if response.Meta.Page == 0 {
						t.Error("Meta page should be set")
					}
					if response.Meta.PageSize == 0 {
						t.Error("Meta page size should be set")
					}
					if response.Meta.Total == 0 {
						t.Error("Meta total should be set")
					}
				}
			}
		})
	}
}

// TestErrorCodeConsistency tests that error codes are consistent
func TestErrorCodeConsistency(t *testing.T) {
	errorCodes := map[string]int{
		ErrCodeNotFound:           http.StatusNotFound,
		ErrCodeUnauthorized:       http.StatusUnauthorized,
		ErrCodeForbidden:          http.StatusForbidden,
		ErrCodeBadRequest:         http.StatusBadRequest,
		ErrCodeConflict:           http.StatusConflict,
		ErrCodeInternalError:      http.StatusInternalServerError,
		ErrCodeValidation:         http.StatusBadRequest,
		ErrCodeTooManyRequests:    http.StatusTooManyRequests,
		ErrCodeServiceUnavailable: http.StatusServiceUnavailable,
	}

	for code, expectedStatus := range errorCodes {
		t.Run("ErrorCode_"+code, func(t *testing.T) {
			ctx := context.Background()
			w := httptest.NewRecorder()

			Error(ctx, w, expectedStatus, code, "test message")

			if w.Code != expectedStatus {
				t.Errorf("Expected status %d for code %s, got %d", expectedStatus, code, w.Code)
			}

			var response Response
			if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
				t.Fatalf("Failed to parse response: %v", err)
			}

			if response.Error.Code != code {
				t.Errorf("Expected error code %s, got %s", code, response.Error.Code)
			}
		})
	}
}

// TestHelperFunctions tests the convenience helper functions
func TestHelperFunctions(t *testing.T) {
	helpers := []struct {
		name           string
		helperFunc     func(ctx context.Context, w http.ResponseWriter, message string)
		expectedCode   string
		expectedStatus int
	}{
		{"BadRequest", BadRequest, ErrCodeBadRequest, http.StatusBadRequest},
		{"Unauthorized", Unauthorized, ErrCodeUnauthorized, http.StatusUnauthorized},
		{"Forbidden", Forbidden, ErrCodeForbidden, http.StatusForbidden},
		{"NotFound", NotFound, ErrCodeNotFound, http.StatusNotFound},
		{"InternalError", InternalError, ErrCodeInternalError, http.StatusInternalServerError},
		{"Conflict", Conflict, ErrCodeConflict, http.StatusConflict},
	}

	for _, helper := range helpers {
		t.Run("Helper_"+helper.name, func(t *testing.T) {
			ctx := context.Background()
			w := httptest.NewRecorder()
			helper.helperFunc(ctx, w, "test message")

			if w.Code != helper.expectedStatus {
				t.Errorf("Expected status %d, got %d", helper.expectedStatus, w.Code)
			}

			var response Response
			if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
				t.Fatalf("Failed to parse response: %v", err)
			}

			if response.Error.Code != helper.expectedCode {
				t.Errorf("Expected error code %s, got %s", helper.expectedCode, response.Error.Code)
			}

			if !strings.Contains(response.Error.Message, "test message") {
				t.Errorf("Expected message to contain 'test message', got %s", response.Error.Message)
			}
		})
	}
}

// TestJSONFallback tests that JSON encoding errors are handled gracefully
func TestJSONFallback(t *testing.T) {
	w := httptest.NewRecorder()

	// Create a data structure that will fail JSON encoding (circular reference)
	circular := make(map[string]interface{})
	circular["self"] = circular

	JSON(w, http.StatusOK, circular)

	// Should return a fallback error instead of panicking
	if w.Code != http.StatusOK {
		// The status was already written, so it stays 200, but the body should contain an error
		var response map[string]interface{}
		if err := json.Unmarshal(w.Body.Bytes(), &response); err == nil {
			if success, ok := response["success"].(bool); ok && success {
				t.Error("Expected error response due to JSON encoding failure")
			}
		}
	}
}

// BenchmarkResponseCreation benchmarks response creation performance
func BenchmarkResponseCreation(b *testing.B) {
	ctx := context.WithValue(context.Background(), constants.ContextKeyRequestID, "bench-request")

	b.Run("Success", func(b *testing.B) {
		data := map[string]string{"test": "data"}
		for i := 0; i < b.N; i++ {
			w := httptest.NewRecorder()
			Success(ctx, w, data)
		}
	})

	b.Run("Error", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			w := httptest.NewRecorder()
			Error(ctx, w, http.StatusBadRequest, ErrCodeBadRequest, "test error")
		}
	})

	b.Run("ValidationError", func(b *testing.B) {
		details := map[string]any{"field": "error"}
		for i := 0; i < b.N; i++ {
			w := httptest.NewRecorder()
			ValidationError(ctx, w, "validation failed", details)
		}
	})
}

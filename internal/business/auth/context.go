// Package auth provides authentication and authorization context utilities
package auth

import (
	"net/http"

	"github.com/koopa0/pms-api-v2/internal/api"
	"github.com/koopa0/pms-api-v2/internal/constants"
)

// GetUserDataFromRequest retrieves user ID and role from request context
// Returns userID, role, and whether the user was found
func GetUserDataFromRequest(r *http.Request) (int32, string, bool) {
	userID, hasUserID := GetUserIDFromContext(r.Context())
	userRole, hasUserRole := GetUserRoleFromContext(r.Context())

	if !hasUserID || !hasUserRole {
		return 0, "", false
	}

	return userID, string(userRole), true
}

// RequireAuth checks if the request has valid authentication
// Returns userID and role if authenticated, writes error response if not
func RequireAuth(w http.ResponseWriter, r *http.Request) (int32, string, bool) {
	userID, userRole, ok := GetUserDataFromRequest(r)
	if !ok {
		api.Unauthorized(r.Context(), w, constants.ErrMsgUnauthorized)
		return 0, "", false
	}
	return userID, userRole, true
}

// RequireRole checks if the user has one of the required roles
// Writes appropriate error response if the user doesn't have a required role
func RequireRole(w http.ResponseWriter, r *http.Request, allowedRoles ...string) (int32, string, bool) {
	userID, userRole, ok := RequireAuth(w, r)
	if !ok {
		return 0, "", false
	}

	for _, allowed := range allowedRoles {
		if userRole == allowed {
			return userID, userRole, true
		}
	}

	api.Forbidden(r.Context(), w, constants.ErrMsgForbidden)
	return 0, "", false
}

// RequireAdmin is a shorthand for requiring SPO role
func RequireAdmin(w http.ResponseWriter, r *http.Request) (int32, bool) {
	userID, _, ok := RequireRole(w, r, string(constants.UserRoles.SPO))
	return userID, ok
}

package testutils

import (
	"context"
	"sync"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// TestCompany represents a test company for database operations
type TestCompany struct {
	ID                int32
	UnifiedBusinessNo string
	Name              string
	Owner             string
	ContactPhone      string
	Address           string
	Type              string
}

// TestProject represents a test project for database operations
type TestProject struct {
	ID          int32
	Name        string
	Status      string
	Description string
}

// PgxDatabaseTestHelper provides helpers for database testing using pgxpool
// This follows Architecture.md guidelines for testing database operations
type PgxDatabaseTestHelper struct {
	pool     *pgxpool.Pool
	queries  *sqlc.Queries
	mu       sync.RWMutex
	testData map[string]interface{}
}

// NewPgxDatabaseTestHelper creates a new database test helper using pgxpool
func NewPgxDatabaseTestHelper(pool *pgxpool.Pool) *PgxDatabaseTestHelper {
	return &PgxDatabaseTestHelper{
		pool:     pool,
		queries:  sqlc.New(pool),
		testData: make(map[string]interface{}),
	}
}

// WithTransaction runs a test function within a database transaction
// The transaction is automatically rolled back after the test
func (h *PgxDatabaseTestHelper) WithTransaction(t *testing.T, fn func(*testing.T, *sqlc.Queries)) {
	t.Helper()

	ctx := context.Background()
	tx, err := h.pool.Begin(ctx)
	if err != nil {
		t.Fatalf("failed to begin transaction: %v", err)
	}
	defer func() {
		if err := tx.Rollback(ctx); err != nil && err.Error() != "tx is closed" {
			t.Logf("failed to rollback transaction: %v", err)
		}
	}()

	queries := h.queries.WithTx(tx)
	fn(t, queries)
}

// CreateTestUser creates a test user with the given data
func (h *PgxDatabaseTestHelper) CreateTestUser(t *testing.T, user TestUser) TestUser {
	t.Helper()

	ctx := context.Background()

	// Hash password
	hashedPassword, err := hashPassword(user.Password)
	if err != nil {
		t.Fatalf("failed to hash password: %v", err)
	}

	// Create user using actual database query
	createdUser, err := h.queries.CreateUser(ctx, sqlc.CreateUserParams{
		Username:     user.Username,
		Email:        user.Email,
		PasswordHash: hashedPassword,
		UserRole:     sqlc.UserRole(user.Role),
		Status:       sqlc.UserStatusValue1, // "異動待審"
	})
	if err != nil {
		t.Fatalf("failed to create test user: %v", err)
	}

	return TestUser{
		ID:       createdUser.ID,
		Username: createdUser.Username,
		Email:    createdUser.Email,
		Role:     string(createdUser.UserRole),
		Password: user.Password, // Keep the original password for testing
	}
}

// CreateTestCompany creates a test company with the given data
func (h *PgxDatabaseTestHelper) CreateTestCompany(t *testing.T, company TestCompany) TestCompany {
	t.Helper()

	ctx := context.Background()

	createdCompany, err := h.queries.CreateCompany(ctx, sqlc.CreateCompanyParams{
		UnifiedBusinessNo: company.UnifiedBusinessNo,
		CompanyName:       company.Name,
		Owner:             company.Owner,
		Phone:             pgtype.Text{String: company.ContactPhone, Valid: company.ContactPhone != ""},
		Address:           pgtype.Text{String: company.Address, Valid: company.Address != ""},
		CompanyType:       sqlc.NullCompanyType{CompanyType: sqlc.CompanyType(company.Type), Valid: company.Type != ""},
	})
	if err != nil {
		t.Fatalf("failed to create test company: %v", err)
	}

	return TestCompany{
		ID:                createdCompany.ID,
		UnifiedBusinessNo: createdCompany.UnifiedBusinessNo,
		Name:              createdCompany.CompanyName,
		Address:           company.Address,
		ContactPhone:      company.ContactPhone,
		Owner:             createdCompany.Owner,
		Type:              company.Type,
	}
}

// CreateTestProject creates a test project with the given data
func (h *PgxDatabaseTestHelper) CreateTestProject(t *testing.T, project TestProject) TestProject {
	t.Helper()

	ctx := context.Background()

	createdProject, err := h.queries.CreateProject(ctx, sqlc.CreateProjectParams{
		Name:                   project.Name,
		Type:                   sqlc.ProjectTypeValue0,     // "一般詢價"
		Category:               sqlc.ProjectCategoryValue0, // "電腦軟體雲端服務"
		Status:                 sqlc.ProjectStatusValue0,   // "進行中"
		CisaFillTimeStart:      pgtype.Timestamp{Valid: false},
		CisaFillTimeEnd:        pgtype.Timestamp{Valid: false},
		CompanyFillTimeStart:   pgtype.Timestamp{Valid: false},
		CompanyFillTimeEnd:     pgtype.Timestamp{Valid: false},
		CompanyCorrectionStart: pgtype.Timestamp{Valid: false},
		CompanyCorrectionEnd:   pgtype.Timestamp{Valid: false},
		AttachmentSpace:        pgtype.Int8{Valid: false},
		RocPriceReferenceYear:  pgtype.Int4{Valid: false},
		Remarks:                pgtype.Text{String: project.Description, Valid: project.Description != ""},
		IsTest:                 true, // Test projects
		CreatedBy:              pgtype.Int4{Int32: 1, Valid: true},
		UpdatedBy:              pgtype.Int4{Int32: 1, Valid: true},
	})
	if err != nil {
		t.Fatalf("failed to create test project: %v", err)
	}

	return TestProject{
		ID:          createdProject.ID,
		Name:        createdProject.Name,
		Status:      string(createdProject.Status),
		Description: project.Description,
	}
}

// CleanupTestData removes all test data created during tests
func (h *PgxDatabaseTestHelper) CleanupTestData(t *testing.T) {
	t.Helper()

	ctx := context.Background()

	// Clean up in reverse order of foreign key dependencies
	tables := []string{
		"project_logs",
		"system_logs",
		"quotes",
		"products",
		"product_groups",
		"projects",
		"password_reset_tokens",
		"users",
		"companies",
		"registration_requests",
	}

	for _, table := range tables {
		query := "TRUNCATE TABLE " + table + " CASCADE"
		if _, err := h.pool.Exec(ctx, query); err != nil {
			t.Logf("failed to truncate table %s: %v", table, err)
		}
	}
}

// GetTestData retrieves test data by key
func (h *PgxDatabaseTestHelper) GetTestData(key string) interface{} {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return h.testData[key]
}

// SetTestData stores test data by key
func (h *PgxDatabaseTestHelper) SetTestData(key string, value interface{}) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.testData[key] = value
}

// Pool returns the underlying pgxpool for advanced operations
func (h *PgxDatabaseTestHelper) Pool() *pgxpool.Pool {
	return h.pool
}

// hashPassword is a simple bcrypt wrapper for testing
func hashPassword(password string) (string, error) {
	// In real implementation, this would use bcrypt
	// For testing, we'll use a simple hash
	return "hashed_" + password, nil
}

package email

import (
	"context"
	"log/slog"
	"os"
	"testing"

	"github.com/koopa0/pms-api-v2/internal/platform/config"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestService_SendRegistrationRejected(t *testing.T) {
	t.<PERSON>()

	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	require.NoError(t, err)

	ctx := context.Background()

	tests := []struct {
		name      string
		to        string
		data      EmailData
		expectErr bool
		errMsg    string
	}{
		{
			name: "valid rejection data",
			to:   "<EMAIL>",
			data: EmailData{
				Username:    "testuser",
				CompanyName: "Test Company",
				Reason:      "Invalid documentation",
			},
			expectErr: false,
		},
		{
			name: "invalid email",
			to:   "invalid-email",
			data: RejectionData{
				Username:    "testuser",
				CompanyName: "Test Company",
				Reason:      "Invalid documentation",
			},
			expectErr: true,
			errMsg:    "invalid email address",
		},
		{
			name: "empty username",
			to:   "<EMAIL>",
			data: RejectionData{
				Username:    "",
				CompanyName: "Test Company",
				Reason:      "Invalid documentation",
			},
			expectErr: true,
			errMsg:    "username cannot be empty",
		},
		{
			name: "empty company name",
			to:   "<EMAIL>",
			data: RejectionData{
				Username:    "testuser",
				CompanyName: "",
				Reason:      "Invalid documentation",
			},
			expectErr: true,
			errMsg:    "company name cannot be empty",
		},
		{
			name: "empty reason",
			to:   "<EMAIL>",
			data: RejectionData{
				Username:    "testuser",
				CompanyName: "Test Company",
				Reason:      "",
			},
			expectErr: true,
			errMsg:    "reason cannot be empty",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := service.SendRegistrationRejected(ctx, tt.to, tt.data)

			if tt.expectErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				// Note: This will fail in actual SMTP sending, but validates the template and data
				if err != nil {
					// Allow SMTP connection errors in tests
					assert.Contains(t, err.Error(), "dial tcp")
				}
			}
		})
	}
}

func TestService_SendPasswordReset(t *testing.T) {
	t.Parallel()

	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	require.NoError(t, err)

	ctx := context.Background()

	tests := []struct {
		name      string
		to        string
		data      PasswordResetData
		expectErr bool
		errMsg    string
	}{
		{
			name: "valid password reset data",
			to:   "<EMAIL>",
			data: PasswordResetData{
				Username:  "testuser",
				ResetLink: "https://example.com/reset?token=abc123",
			},
			expectErr: false,
		},
		{
			name: "invalid email",
			to:   "invalid-email",
			data: PasswordResetData{
				Username:  "testuser",
				ResetLink: "https://example.com/reset?token=abc123",
			},
			expectErr: true,
			errMsg:    "invalid email address",
		},
		{
			name: "empty username",
			to:   "<EMAIL>",
			data: PasswordResetData{
				Username:  "",
				ResetLink: "https://example.com/reset?token=abc123",
			},
			expectErr: true,
			errMsg:    "username cannot be empty",
		},
		{
			name: "empty reset link",
			to:   "<EMAIL>",
			data: PasswordResetData{
				Username:  "testuser",
				ResetLink: "",
			},
			expectErr: true,
			errMsg:    "reset link cannot be empty",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := service.SendPasswordReset(ctx, tt.to, tt.data)

			if tt.expectErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				// Note: This will fail in actual SMTP sending, but validates the template and data
				if err != nil {
					// Allow SMTP connection errors in tests
					assert.Contains(t, err.Error(), "dial tcp")
				}
			}
		})
	}
}

func TestService_SendReminder(t *testing.T) {
	t.Parallel()

	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	require.NoError(t, err)

	ctx := context.Background()

	tests := []struct {
		name      string
		to        string
		data      ReminderData
		expectErr bool
		errMsg    string
	}{
		{
			name: "valid reminder data",
			to:   "<EMAIL>",
			data: ReminderData{
				CompanyName: "Test Company",
				ProjectName: "Test Project",
				DueDate:     "2024-12-31",
				LoginURL:    "https://example.com/login",
			},
			expectErr: false,
		},
		{
			name: "invalid email",
			to:   "invalid-email",
			data: ReminderData{
				CompanyName: "Test Company",
				ProjectName: "Test Project",
				DueDate:     "2024-12-31",
				LoginURL:    "https://example.com/login",
			},
			expectErr: true,
			errMsg:    "invalid email address",
		},
		{
			name: "empty company name",
			to:   "<EMAIL>",
			data: ReminderData{
				CompanyName: "",
				ProjectName: "Test Project",
				DueDate:     "2024-12-31",
				LoginURL:    "https://example.com/login",
			},
			expectErr: true,
			errMsg:    "company name cannot be empty",
		},
		{
			name: "empty project name",
			to:   "<EMAIL>",
			data: ReminderData{
				CompanyName: "Test Company",
				ProjectName: "",
				DueDate:     "2024-12-31",
				LoginURL:    "https://example.com/login",
			},
			expectErr: true,
			errMsg:    "project name cannot be empty",
		},
		{
			name: "empty due date",
			to:   "<EMAIL>",
			data: ReminderData{
				CompanyName: "Test Company",
				ProjectName: "Test Project",
				DueDate:     "",
				LoginURL:    "https://example.com/login",
			},
			expectErr: true,
			errMsg:    "due date cannot be empty",
		},
		{
			name: "empty login URL",
			to:   "<EMAIL>",
			data: ReminderData{
				CompanyName: "Test Company",
				ProjectName: "Test Project",
				DueDate:     "2024-12-31",
				LoginURL:    "",
			},
			expectErr: true,
			errMsg:    "login URL cannot be empty",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := service.SendReminder(ctx, tt.to, tt.data)

			if tt.expectErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				// Note: This will fail in actual SMTP sending, but validates the template and data
				if err != nil {
					// Allow SMTP connection errors in tests
					assert.Contains(t, err.Error(), "dial tcp")
				}
			}
		})
	}
}

func TestService_parseTemplate(t *testing.T) {
	t.Parallel()

	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	require.NoError(t, err)

	tests := []struct {
		name         string
		templateName string
		data         interface{}
		expectErr    bool
	}{
		{
			name:         "valid template with data",
			templateName: "registration_confirmation.html",
			data: RegistrationData{
				Username:    "testuser",
				CompanyName: "Test Company",
			},
			expectErr: false,
		},
		{
			name:         "non-existent template",
			templateName: "non_existent.html",
			data:         map[string]string{"key": "value"},
			expectErr:    true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result, err := service.parseTemplate(tt.templateName, tt.data)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Empty(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, result)
				// Should contain the username in the parsed template
				if data, ok := tt.data.(RegistrationData); ok {
					assert.Contains(t, result, data.Username)
				}
			}
		})
	}
}

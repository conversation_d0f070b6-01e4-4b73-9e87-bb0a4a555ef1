package registration

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/koopa0/pms-api-v2/internal/api"
	"github.com/koopa0/pms-api-v2/internal/business/auth"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Handler handles HTTP requests for registration endpoints
type Handler struct {
	service *Service
}

// NewHandler creates a new registration handler
func NewHandler(service *Service) *Handler {
	return &Handler{
		service: service,
	}
}

// RegisterRequest represents the HTTP request body for registration
type RegisterRequest struct {
	CompanyName   string           `json:"company_name" validate:"required,max=200"`
	CompanyType   sqlc.CompanyType `json:"company_type" validate:"required,oneof=軟體廠商 資訊服務廠商"`
	UniformNumber string           `json:"uniform_number" validate:"required,len=8,numeric"`
	CompanyOwner  string           `json:"company_owner" validate:"required,max=100"`
	Address       string           `json:"address" validate:"max=500"`
	ContactPerson string           `json:"contact_person" validate:"required,max=100"`
	JobTitle      string           `json:"job_title" validate:"max=100"`
	ContactPhone  string           `json:"contact_phone" validate:"max=20"`
	ContactMobile string           `json:"contact_mobile" validate:"max=20"`
	ContactEmail  string           `json:"contact_email" validate:"required,email,max=100"`
	BackupEmail   string           `json:"backup_email" validate:"omitempty,email,max=100"`
	Username      string           `json:"username" validate:"required,min=3,max=100,alphanum"`
	Password      string           `json:"password" validate:"required,min=8"`
}

// ApproveRequest represents the HTTP request body for approval
type ApproveRequest struct {
	// Empty body - approval doesn't need additional data
}

// RejectRequest represents the HTTP request body for rejection
type RejectRequest struct {
	Reason string `json:"reason" validate:"required,max=500"`
}

// Register handles new registration requests
// POST /api/v1/register
func (h *Handler) Register(w http.ResponseWriter, r *http.Request) {
	var req RegisterRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.BadRequest(r.Context(), w, "無效的請求格式")
		return
	}

	// Convert to service request
	createReq := &CreateRegistrationRequest{
		CompanyName:   req.CompanyName,
		CompanyType:   req.CompanyType,
		UniformNumber: req.UniformNumber,
		CompanyOwner:  req.CompanyOwner,
		Address:       req.Address,
		ContactPerson: req.ContactPerson,
		JobTitle:      req.JobTitle,
		ContactPhone:  req.ContactPhone,
		ContactMobile: req.ContactMobile,
		ContactEmail:  req.ContactEmail,
		BackupEmail:   req.BackupEmail,
		Username:      req.Username,
		Password:      req.Password,
	}

	// Create registration
	registration, err := h.service.Create(r.Context(), createReq)
	if err != nil {
		h.handleRegistrationError(r.Context(), w, err)
		return
	}

	// Return success response with status 201
	responseData := map[string]interface{}{
		"message":         constants.MsgRegistered,
		"registration_id": registration.ID,
		"status":          registration.Status,
		"created_at":      registration.CreatedAt,
	}

	// Use the enhanced JSON response with proper status code
	response := api.Response{
		Success:   true,
		Data:      responseData,
		RequestID: getRequestIDFromContext(r.Context()),
		Timestamp: time.Now(),
	}
	api.JSON(w, http.StatusCreated, response)
}

// ListPending handles listing pending registrations (admin only)
// GET /api/v1/registrations/pending
func (h *Handler) ListPending(w http.ResponseWriter, r *http.Request) {
	// Check authentication
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		api.Unauthorized(r.Context(), w, constants.ErrMsgUnauthorized)
		return
	}

	// Check permission - only CISA and SPO can review
	if claims.Role != "CISA" && claims.Role != "SPO" {
		api.Forbidden(r.Context(), w, constants.ErrMsgAdminRequired)
		return
	}

	// Parse pagination
	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	if page < 1 {
		page = 1
	}
	perPage, _ := strconv.Atoi(r.URL.Query().Get("per_page"))
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	offset := int32((page - 1) * perPage)
	limit := int32(perPage)

	// Get pending registrations
	registrations, err := h.service.ListPendingRegistrations(r.Context(), offset, limit)
	if err != nil {
		errMsg := api.DBErrorMessage(err, "獲取待審核註冊申請")
		api.InternalError(r.Context(), w, errMsg)
		return
	}

	// Format response
	items := make([]map[string]interface{}, len(registrations))
	for i, reg := range registrations {
		items[i] = map[string]interface{}{
			"id":             reg.ID,
			"company_name":   reg.CompanyName,
			"uniform_number": reg.UnifiedBusinessNo,
			"contact_person": reg.ContactPerson,
			"contact_email":  reg.Email,
			"status":         reg.Status,
			"created_at":     reg.CreatedAt,
		}
	}

	api.Success(r.Context(), w, map[string]interface{}{
		"items":    items,
		"page":     page,
		"per_page": perPage,
		"total":    len(items),
	})
}

// GetRegistration handles getting a single registration by ID (admin only)
// GET /api/v1/registrations/{id}
func (h *Handler) GetRegistration(w http.ResponseWriter, r *http.Request) {
	// Check authentication
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		api.Unauthorized(r.Context(), w, "請先登入")
		return
	}

	// Check permission
	if claims.Role != "CISA" && claims.Role != "SPO" {
		api.Forbidden(r.Context(), w, "權限不足")
		return
	}

	// Get ID from URL
	idStr := r.PathValue("id")
	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		api.BadRequest(r.Context(), w, "無效的 ID")
		return
	}

	// Get registration
	registration, err := h.service.GetRegistrationByID(r.Context(), int32(id))
	if err != nil {
		api.NotFound(r.Context(), w, constants.ErrMsgRegistrationNotFound)
		return
	}

	// Return detailed registration data
	api.Success(r.Context(), w, map[string]interface{}{
		"id":             registration.ID,
		"company_name":   registration.CompanyName,
		"company_type":   registration.CompanyType,
		"uniform_number": registration.UnifiedBusinessNo,
		"company_owner":  registration.CompanyOwner,
		"address":        registration.Address,
		"contact_person": registration.ContactPerson,
		"job_title":      registration.JobTitle,
		"phone":          registration.Phone,
		"mobile":         registration.Mobile,
		"email":          registration.Email,
		"backup_email":   registration.BackupEmail,
		"status":         registration.Status,
		"remark":         registration.Remark,
		"review_remark":  registration.ReviewRemark,
		"created_at":     registration.CreatedAt,
		"reviewed_at":    registration.ReviewedAt,
		"reviewed_by":    registration.ReviewedBy,
	})
}

// Approve handles approval of a registration request (admin only)
// PUT /api/v1/registrations/{id}/approve
func (h *Handler) Approve(w http.ResponseWriter, r *http.Request) {
	// Check authentication
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		api.Unauthorized(r.Context(), w, "請先登入")
		return
	}

	// Check permission
	if claims.Role != "CISA" && claims.Role != "SPO" {
		api.Forbidden(r.Context(), w, "權限不足")
		return
	}

	// Get ID from URL
	idStr := r.PathValue("id")
	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		api.BadRequest(r.Context(), w, "無效的 ID")
		return
	}

	// Approve registration
	err = h.service.ApproveRegistration(r.Context(), int32(id), claims.UserID)
	if err != nil {
		h.handleApprovalError(r.Context(), w, err)
		return
	}

	api.Success(r.Context(), w, map[string]interface{}{
		"message": constants.MsgRegistrationApproved,
	})
}

// Reject handles rejection of a registration request (admin only)
// PUT /api/v1/registrations/{id}/reject
func (h *Handler) Reject(w http.ResponseWriter, r *http.Request) {
	// Check authentication
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		api.Unauthorized(r.Context(), w, "請先登入")
		return
	}

	// Check permission
	if claims.Role != "CISA" && claims.Role != "SPO" {
		api.Forbidden(r.Context(), w, "權限不足")
		return
	}

	// Get ID from URL
	idStr := r.PathValue("id")
	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		api.BadRequest(r.Context(), w, "無效的 ID")
		return
	}

	// Parse request body
	var req RejectRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.BadRequest(r.Context(), w, "無效的請求格式")
		return
	}

	if req.Reason == "" {
		api.BadRequest(r.Context(), w, "退件原因不能為空")
		return
	}

	// Reject registration
	err = h.service.RejectRegistration(r.Context(), int32(id), claims.UserID, req.Reason)
	if err != nil {
		h.handleApprovalError(r.Context(), w, err)
		return
	}

	api.Success(r.Context(), w, map[string]interface{}{
		"message": constants.MsgRegistrationRejected,
	})
}

// handleRegistrationError handles registration-specific errors
// 使用新的資料庫錯誤處理系統提供更精確的錯誤訊息
func (h *Handler) handleRegistrationError(ctx context.Context, w http.ResponseWriter, err error) {
	// 先檢查是否為資料庫錯誤，提供更精確的錯誤訊息
	errMsg := api.DBErrorMessage(err, "註冊申請")
	if errMsg != "註冊申請失敗" {
		// 成功獲得具體錯誤訊息，直接使用
		api.InternalError(ctx, w, errMsg)
		return
	}

	// 特定業務錯誤的處理
	switch err.Error() {
	case constants.ErrMsgUserAlreadyExists:
		api.Conflict(ctx, w, constants.ErrMsgUserAlreadyExists)
	case constants.ErrMsgTaxIDAlreadyExists:
		api.Conflict(ctx, w, constants.ErrMsgTaxIDAlreadyExists)
	case constants.ErrMsgRegistrationAlreadyExists:
		api.Conflict(ctx, w, constants.ErrMsgRegistrationAlreadyExists)
	case constants.ErrMsgCompanyNameRequired, constants.ErrMsgTaxIDRequired:
		api.BadRequest(ctx, w, err.Error())
	default:
		if strings.Contains(err.Error(), "不能為空") || strings.Contains(err.Error(), "長度") || strings.Contains(err.Error(), "無效") {
			api.BadRequest(ctx, w, err.Error())
		} else {
			api.InternalError(ctx, w, constants.ErrMsgOperationFailed)
		}
	}
}

// handleApprovalError handles approval/rejection specific errors
// 使用新的資料庫錯誤處理系統提供更精確的錯誤訊息
func (h *Handler) handleApprovalError(ctx context.Context, w http.ResponseWriter, err error) {
	// 先檢查是否為資料庫錯誤，提供更精確的錯誤訊息
	errMsg := api.DBErrorMessage(err, "註冊審核")
	if errMsg != "註冊審核失敗" {
		// 成功獲得具體錯誤訊息，直接使用
		api.InternalError(ctx, w, errMsg)
		return
	}

	// 特定業務錯誤的處理
	switch err.Error() {
	case constants.ErrMsgRegistrationNotFound:
		api.NotFound(ctx, w, constants.ErrMsgRegistrationNotFound)
	case constants.ErrMsgRegistrationProcessed:
		api.Conflict(ctx, w, constants.ErrMsgRegistrationProcessed)
	case constants.ErrMsgRejectReasonRequired:
		api.BadRequest(ctx, w, constants.ErrMsgRejectReasonRequired)
	default:
		api.InternalError(ctx, w, constants.ErrMsgOperationFailed)
	}
}

// getRequestIDFromContext retrieves the request ID from the context
func getRequestIDFromContext(ctx context.Context) string {
	if requestID, ok := ctx.Value(constants.ContextKeyRequestID).(string); ok {
		return requestID
	}
	return ""
}

// Package types provide type conversion utilities, pointer helpers,
// and pagination types for consistent value/pointer semantics across the codebase
package types

import (
	"database/sql"
	"fmt"
	"math"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

// Time is a time.Time wrapper for JSON serialization
type Time time.Time

// NullTime represents a nullable time value
type NullTime struct {
	Time  time.Time
	Valid bool
}

// MarshalJSON implements json.Marshaler for Time
func (t Time) MarshalJSON() ([]byte, error) {
	return time.Time(t).MarshalJSON()
}

// UnmarshalJSON implements json.Unmarshaler for Time
func (t *Time) UnmarshalJSON(data []byte) error {
	var timeVal time.Time
	if err := timeVal.UnmarshalJSON(data); err != nil {
		return err
	}
	*t = Time(timeVal)
	return nil
}

// String returns the string representation of Time
func (t Time) String() string {
	return time.Time(t).String()
}

// SafeInt32 safely converts int to int32 with overflow protection
// Returns math.MaxInt32 if the input exceeds the int32 range
func SafeInt32(i int) int32 {
	if i > math.MaxInt32 {
		return math.MaxInt32
	}
	if i < math.MinInt32 {
		return math.MinInt32
	}
	return int32(i)
}

// SafeInt safely converts int32 to int
// No overflow possible since int is at least 32 bits
func SafeInt(i int32) int {
	return int(i)
}

// Int32Ptr returns a pointer to the int32 value
func Int32Ptr(i int32) *int32 {
	return &i
}

// IntPtr returns a pointer to the int value
func IntPtr(i int) *int {
	return &i
}

// StringPtr returns a pointer to the string value
func StringPtr(s string) *string {
	return &s
}

// BoolPtr returns a pointer to the bool value
func BoolPtr(b bool) *bool {
	return &b
}

// Float64Ptr returns a pointer to the float64 value
func Float64Ptr(f float64) *float64 {
	return &f
}

// NullStringFromPtr creates sql.NullString from string pointer
// Returns invalid NullString if pointer is nil
func NullStringFromPtr(s *string) sql.NullString {
	if s == nil {
		return sql.NullString{Valid: false}
	}
	return sql.NullString{String: *s, Valid: true}
}

// NullString creates sql.NullString from string value
// Returns invalid NullString if string is empty
func NullString(s string) sql.NullString {
	if s == "" {
		return sql.NullString{Valid: false}
	}
	return sql.NullString{String: s, Valid: true}
}

// NullInt32FromPtr creates sql.NullInt32 from int32 pointer
func NullInt32FromPtr(i *int32) sql.NullInt32 {
	if i == nil {
		return sql.NullInt32{Valid: false}
	}
	return sql.NullInt32{Int32: *i, Valid: true}
}

// NullInt32 creates sql.NullInt32 from int32 value
func NullInt32(i int32) sql.NullInt32 {
	return sql.NullInt32{Int32: i, Valid: true}
}

// StringPtrFromNull extracts string pointer from sql.NullString
// Returns nil if NullString is invalid
func StringPtrFromNull(ns sql.NullString) *string {
	if !ns.Valid {
		return nil
	}
	return &ns.String
}

// Int32PtrFromNull extracts int32 pointer from sql.NullInt32
// Returns nil if NullInt32 is invalid
func Int32PtrFromNull(ni sql.NullInt32) *int32 {
	if !ni.Valid {
		return nil
	}
	return &ni.Int32
}

// ValueOrDefault returns the value if pointer is not nil, otherwise returns default
func ValueOrDefault[T any](ptr *T, defaultVal T) T {
	if ptr == nil {
		return defaultVal
	}
	return *ptr
}

// IsNilOrEmpty checks if string pointer is nil or points to empty string
func IsNilOrEmpty(s *string) bool {
	return s == nil || *s == ""
}

// IsValidID checks if an ID is valid (> 0)
func IsValidID(id int32) bool {
	return id > 0
}

// CoalesceString returns the first non-empty string from the arguments
func CoalesceString(strings ...string) string {
	for _, s := range strings {
		if s != "" {
			return s
		}
	}
	return ""
}

// CoalesceStringPtr returns the first non-nil, non-empty string pointer
func CoalesceStringPtr(ptrs ...*string) *string {
	for _, ptr := range ptrs {
		if !IsNilOrEmpty(ptr) {
			return ptr
		}
	}
	return nil
}

// Conversion functions between pgtype and sql null types

// PgTypeTextToNullString converts pgtype.Text to sql.NullString
func PgTypeTextToNullString(t pgtype.Text) sql.NullString {
	return sql.NullString{String: t.String, Valid: t.Valid}
}

// PgTypeInt4ToNullInt32 converts pgtype.Int4 to sql.NullInt32
func PgTypeInt4ToNullInt32(i pgtype.Int4) sql.NullInt32 {
	return sql.NullInt32{Int32: i.Int32, Valid: i.Valid}
}

// PgTypeTimestampToTime converts pgtype.Timestamp to time.Time
func PgTypeTimestampToTime(ts pgtype.Timestamp) time.Time {
	if !ts.Valid {
		return time.Time{}
	}
	return ts.Time
}

// TimeToPgTypeTimestamp converts time.Time to pgtype.Timestamp
func TimeToPgTypeTimestamp(t time.Time) pgtype.Timestamp {
	if t.IsZero() {
		return pgtype.Timestamp{Valid: false}
	}
	return pgtype.Timestamp{Time: t, Valid: true}
}

// StringPtrFromPgTypeText extracts string pointer from pgtype.Text
func StringPtrFromPgTypeText(t pgtype.Text) *string {
	if !t.Valid {
		return nil
	}
	return &t.String
}

// Int32PtrFromPgTypeInt4 extracts int32 pointer from pgtype.Int4
func Int32PtrFromPgTypeInt4(i pgtype.Int4) *int32 {
	if !i.Valid {
		return nil
	}
	return &i.Int32
}

// NullStringToPgTypeText converts sql.NullString to pgtype.Text
func NullStringToPgTypeText(ns sql.NullString) pgtype.Text {
	return pgtype.Text{String: ns.String, Valid: ns.Valid}
}

// NullInt32ToPgTypeInt4 converts sql.NullInt32 to pgtype.Int4
func NullInt32ToPgTypeInt4(ni sql.NullInt32) pgtype.Int4 {
	return pgtype.Int4{Int32: ni.Int32, Valid: ni.Valid}
}

// NullTimeToPgTypeTimestamp converts sql.NullTime to pgtype.Timestamp
func NullTimeToPgTypeTimestamp(nt sql.NullTime) pgtype.Timestamp {
	return pgtype.Timestamp{Time: nt.Time, Valid: nt.Valid}
}

// PgTypeTextFromStringPtr creates pgtype.Text from string pointer
func PgTypeTextFromStringPtr(s *string) pgtype.Text {
	if s == nil {
		return pgtype.Text{Valid: false}
	}
	return pgtype.Text{String: *s, Valid: true}
}

// PgTypeInt4FromInt32Ptr creates pgtype.Int4 from int32 pointer
func PgTypeInt4FromInt32Ptr(i *int32) pgtype.Int4 {
	if i == nil {
		return pgtype.Int4{Valid: false}
	}
	return pgtype.Int4{Int32: *i, Valid: true}
}

// Int32PtrToPgTypeInt4 is an alias for PgTypeInt4FromInt32Ptr for consistency
func Int32PtrToPgTypeInt4(i *int32) pgtype.Int4 {
	return PgTypeInt4FromInt32Ptr(i)
}

// Float64ToNumeric converts float64 pointer to pgtype.Numeric
func Float64ToNumeric(f *float64) pgtype.Numeric {
	if f == nil {
		return pgtype.Numeric{Valid: false}
	}
	// Create numeric from string representation
	numStr := fmt.Sprintf("%f", *f)
	var num pgtype.Numeric
	_ = num.Scan(numStr)
	return num
}

// NumericToFloat64 converts pgtype.Numeric to float64 pointer
func NumericToFloat64(n pgtype.Numeric) *float64 {
	if !n.Valid {
		return nil
	}
	// Convert to float64
	floatVal, err := n.Float64Value()
	if err != nil {
		return nil
	}
	if !floatVal.Valid {
		return nil
	}
	return &floatVal.Float64
}

package email

import (
	"context"
	"log/slog"
	"os"
	"testing"

	"github.com/koopa0/pms-api-v2/internal/platform/config"
)

func BenchmarkNew(b *testing.B) {
	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := New(config, logger)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkService_validateEmailAddress(b *testing.B) {
	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	if err != nil {
		b.Fatal(err)
	}

	emails := []string{
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"invalid-email",
		"test@",
		"@example.com",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		email := emails[i%len(emails)]
		_ = service.validateEmailAddress(email)
	}
}

func BenchmarkService_parseTemplate_RegistrationConfirmation(b *testing.B) {
	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	if err != nil {
		b.Fatal(err)
	}

	data := RegistrationData{
		Username:    "testuser",
		CompanyName: "Test Company",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.parseTemplate("registration_confirmation.html", data)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkService_parseTemplate_RegistrationApproved(b *testing.B) {
	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	if err != nil {
		b.Fatal(err)
	}

	data := ApprovalData{
		Username:    "testuser",
		CompanyName: "Test Company",
		LoginURL:    "https://example.com/login",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.parseTemplate("registration_approved.html", data)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkService_parseTemplate_PasswordReset(b *testing.B) {
	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	if err != nil {
		b.Fatal(err)
	}

	data := PasswordResetData{
		Username:  "testuser",
		ResetLink: "https://example.com/reset?token=abc123",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.parseTemplate("password_reset.html", data)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkService_SendEmail_Disabled(b *testing.B) {
	config := &config.EmailConfig{
		Enabled: false,
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	if err != nil {
		b.Fatal(err)
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := service.SendEmail(ctx, "<EMAIL>", "Test Subject", "Test Body", "")
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkService_SendRegistrationConfirmation_Validation(b *testing.B) {
	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	if err != nil {
		b.Fatal(err)
	}

	ctx := context.Background()
	data := RegistrationData{
		Username:    "testuser",
		CompanyName: "Test Company",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// This will fail at SMTP sending but will benchmark validation and template parsing
		_ = service.SendRegistrationConfirmation(ctx, "<EMAIL>", data)
	}
}

func BenchmarkService_SendRegistrationApproved_Validation(b *testing.B) {
	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	if err != nil {
		b.Fatal(err)
	}

	ctx := context.Background()
	data := ApprovalData{
		Username:    "testuser",
		CompanyName: "Test Company",
		LoginURL:    "https://example.com/login",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// This will fail at SMTP sending but will benchmark validation and template parsing
		_ = service.SendRegistrationApproved(ctx, "<EMAIL>", data)
	}
}

func BenchmarkService_SendPasswordReset_Validation(b *testing.B) {
	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	if err != nil {
		b.Fatal(err)
	}

	ctx := context.Background()
	data := PasswordResetData{
		Username:  "testuser",
		ResetLink: "https://example.com/reset?token=abc123",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// This will fail at SMTP sending but will benchmark validation and template parsing
		_ = service.SendPasswordReset(ctx, "<EMAIL>", data)
	}
}

func BenchmarkService_IsEnabled(b *testing.B) {
	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	if err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = service.IsEnabled()
	}
}

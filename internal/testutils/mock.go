package testutils

import (
	"errors"
	"sync"
	"testing"
)

// MockService provides a base for creating mock services with call tracking
// This follows the Architecture.md principle of "discover abstractions, don't create them"
// by providing real behavior tracking rather than just interface mocking
type MockService struct {
	mu          sync.RWMutex
	CallCount   map[string]int
	CallArgs    map[string][]interface{}
	ReturnValue map[string]interface{}
	ReturnError map[string]error
}

// NewMockService creates a new mock service instance
func NewMockService() *MockService {
	return &MockService{
		CallCount:   make(map[string]int),
		CallArgs:    make(map[string][]interface{}),
		ReturnValue: make(map[string]interface{}),
		ReturnError: make(map[string]error),
	}
}

// RecordCall records a method call for verification
func (m *MockService) RecordCall(method string, args ...interface{}) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.CallCount[method]++
	m.CallArgs[method] = append(m.CallArgs[method], args)
}

// SetReturn sets the return value for a method
func (m *MockService) SetReturn(method string, value interface{}, err error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.ReturnValue[method] = value
	m.ReturnError[method] = err
}

// GetCallCount returns the number of times a method was called
func (m *MockService) GetCallCount(method string) int {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.CallCount[method]
}

// GetCallArgs returns the arguments for all calls to a method
func (m *MockService) GetCallArgs(method string) []interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.CallArgs[method]
}

// GetReturn returns the configured return values for a method
func (m *MockService) GetReturn(method string) (interface{}, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.ReturnValue[method], m.ReturnError[method]
}

// AssertCalled verifies a method was called expected number of times
func (m *MockService) AssertCalled(t *testing.T, method string, expectedCount int) {
	t.Helper()
	actual := m.GetCallCount(method)
	if actual != expectedCount {
		t.Errorf("Expected %s to be called %d times, but was called %d times", method, expectedCount, actual)
	}
}

// AssertNotCalled verifies a method was not called
func (m *MockService) AssertNotCalled(t *testing.T, method string) {
	t.Helper()
	m.AssertCalled(t, method, 0)
}

// AssertCalledWith verifies a method was called with specific arguments
func (m *MockService) AssertCalledWith(t *testing.T, method string, expectedArgs ...interface{}) {
	t.Helper()

	args := m.GetCallArgs(method)
	if len(args) == 0 {
		t.Errorf("Expected %s to be called with arguments, but it was not called", method)
		return
	}

	// Check the last call arguments
	lastCall := args[len(args)-1]
	if len(lastCall.([]interface{})) != len(expectedArgs) {
		t.Errorf("Expected %s to be called with %d arguments, but got %d", method, len(expectedArgs), len(lastCall.([]interface{})))
		return
	}

	// Compare arguments (basic comparison)
	for i, expected := range expectedArgs {
		actual := lastCall.([]interface{})[i]
		if actual != expected {
			t.Errorf("Expected %s argument %d to be %v, but got %v", method, i, expected, actual)
		}
	}
}

// Reset clears all recorded calls and return values
func (m *MockService) Reset() {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.CallCount = make(map[string]int)
	m.CallArgs = make(map[string][]interface{})
	m.ReturnValue = make(map[string]interface{})
	m.ReturnError = make(map[string]error)
}

// MockHTTPClient provides a mock HTTP client for testing external API calls
type MockHTTPClient struct {
	*MockService
	responses map[string]*MockHTTPResponse
}

// MockHTTPResponse represents a mock HTTP response
type MockHTTPResponse struct {
	StatusCode int
	Body       []byte
	Headers    map[string]string
	Error      error
}

// NewMockHTTPClient creates a new mock HTTP client
func NewMockHTTPClient() *MockHTTPClient {
	return &MockHTTPClient{
		MockService: NewMockService(),
		responses:   make(map[string]*MockHTTPResponse),
	}
}

// SetResponse sets a mock response for a specific URL
func (m *MockHTTPClient) SetResponse(url string, response *MockHTTPResponse) {
	m.responses[url] = response
}

// GetResponse returns the mock response for a URL
func (m *MockHTTPClient) GetResponse(url string) *MockHTTPResponse {
	return m.responses[url]
}

// FakeEmailSender provides a fake email sender for testing
type FakeEmailSender struct {
	SentEmails []EmailMessage
	ShouldFail bool
	mu         sync.RWMutex
}

// EmailMessage represents an email message for testing
type EmailMessage struct {
	To      string
	Subject string
	Body    string
	IsHTML  bool
}

// NewFakeEmailSender creates a new fake email sender
func NewFakeEmailSender() *FakeEmailSender {
	return &FakeEmailSender{
		SentEmails: make([]EmailMessage, 0),
	}
}

// SendEmail simulates sending an email
func (f *FakeEmailSender) SendEmail(to, subject, body string, isHTML bool) error {
	f.mu.Lock()
	defer f.mu.Unlock()

	if f.ShouldFail {
		return &MockError{Message: "email service unavailable"}
	}

	f.SentEmails = append(f.SentEmails, EmailMessage{
		To:      to,
		Subject: subject,
		Body:    body,
		IsHTML:  isHTML,
	})

	return nil
}

// GetSentEmails returns all sent emails
func (f *FakeEmailSender) GetSentEmails() []EmailMessage {
	f.mu.RLock()
	defer f.mu.RUnlock()

	return append([]EmailMessage(nil), f.SentEmails...)
}

// Reset clears all sent emails
func (f *FakeEmailSender) Reset() {
	f.mu.Lock()
	defer f.mu.Unlock()

	f.SentEmails = f.SentEmails[:0]
}

// MockError represents a mock error for testing
type MockError struct {
	Message string
	Code    int
}

// Error implements the error interface
func (e *MockError) Error() string {
	return e.Message
}

// Is implements error comparison
func (e *MockError) Is(target error) bool {
	var me *MockError
	if errors.As(target, &me) {
		return e.Code == me.Code
	}
	return false
}

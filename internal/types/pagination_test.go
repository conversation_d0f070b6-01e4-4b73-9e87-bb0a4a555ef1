package types

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewPaginationParams(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		page         int
		pageSize     int
		expectedPage int
		expectedSize int
	}{
		{
			name:         "valid parameters",
			page:         2,
			pageSize:     10,
			expectedPage: 2,
			expectedSize: 10,
		},
		{
			name:         "page less than 1",
			page:         0,
			pageSize:     10,
			expectedPage: 1,
			expectedSize: 10,
		},
		{
			name:         "negative page",
			page:         -5,
			pageSize:     10,
			expectedPage: 1,
			expectedSize: 10,
		},
		{
			name:         "page size less than 1",
			page:         1,
			pageSize:     0,
			expectedPage: 1,
			expectedSize: 20, // default
		},
		{
			name:         "negative page size",
			page:         1,
			pageSize:     -10,
			expectedPage: 1,
			expectedSize: 20, // default
		},
		{
			name:         "page size exceeds maximum",
			page:         1,
			pageSize:     150,
			expectedPage: 1,
			expectedSize: 100, // maximum
		},
		{
			name:         "both invalid",
			page:         -1,
			pageSize:     -1,
			expectedPage: 1,
			expectedSize: 20, // default
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			result := NewPaginationParams(tt.page, tt.pageSize)
			assert.Equal(t, tt.expectedPage, result.Page)
			assert.Equal(t, tt.expectedSize, result.PageSize)
		})
	}
}

func TestPaginationParams_Offset(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		params   PaginationParams
		expected int
	}{
		{
			name:     "first page",
			params:   PaginationParams{Page: 1, PageSize: 10},
			expected: 0,
		},
		{
			name:     "second page",
			params:   PaginationParams{Page: 2, PageSize: 10},
			expected: 10,
		},
		{
			name:     "third page with different size",
			params:   PaginationParams{Page: 3, PageSize: 25},
			expected: 50,
		},
		{
			name:     "large page number",
			params:   PaginationParams{Page: 100, PageSize: 20},
			expected: 1980,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			result := tt.params.Offset()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestPaginationParams_Limit(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		params   PaginationParams
		expected int
	}{
		{
			name:     "standard limit",
			params:   PaginationParams{Page: 1, PageSize: 10},
			expected: 10,
		},
		{
			name:     "large limit",
			params:   PaginationParams{Page: 1, PageSize: 100},
			expected: 100,
		},
		{
			name:     "small limit",
			params:   PaginationParams{Page: 1, PageSize: 1},
			expected: 1,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			result := tt.params.Limit()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestNewPaginatedResponse(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		data           []string
		params         PaginationParams
		total          int
		expectedPages  int
		expectedNext   bool
		expectedPrev   bool
		expectedTotal  int
	}{
		{
			name:           "first page with more data",
			data:           []string{"item1", "item2"},
			params:         PaginationParams{Page: 1, PageSize: 10},
			total:          25,
			expectedPages:  3,
			expectedNext:   true,
			expectedPrev:   false,
			expectedTotal:  25,
		},
		{
			name:           "middle page",
			data:           []string{"item1", "item2"},
			params:         PaginationParams{Page: 2, PageSize: 10},
			total:          25,
			expectedPages:  3,
			expectedNext:   true,
			expectedPrev:   true,
			expectedTotal:  25,
		},
		{
			name:           "last page",
			data:           []string{"item1", "item2"},
			params:         PaginationParams{Page: 3, PageSize: 10},
			total:          25,
			expectedPages:  3,
			expectedNext:   false,
			expectedPrev:   true,
			expectedTotal:  25,
		},
		{
			name:           "single page",
			data:           []string{"item1"},
			params:         PaginationParams{Page: 1, PageSize: 10},
			total:          1,
			expectedPages:  1,
			expectedNext:   false,
			expectedPrev:   false,
			expectedTotal:  1,
		},
		{
			name:           "empty data",
			data:           []string{},
			params:         PaginationParams{Page: 1, PageSize: 10},
			total:          0,
			expectedPages:  0,
			expectedNext:   false,
			expectedPrev:   false,
			expectedTotal:  0,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			result := NewPaginatedResponse(tt.data, tt.params, tt.total)
			
			assert.Equal(t, tt.data, result.Data)
			assert.True(t, result.Success)
			assert.Equal(t, tt.params.Page, result.Pagination.Page)
			assert.Equal(t, tt.params.PageSize, result.Pagination.PageSize)
			assert.Equal(t, tt.expectedTotal, result.Pagination.Total)
			assert.Equal(t, tt.expectedPages, result.Pagination.TotalPages)
			assert.Equal(t, tt.expectedNext, result.Pagination.HasNext)
			assert.Equal(t, tt.expectedPrev, result.Pagination.HasPrevious)
		})
	}
}

func TestEmptyPaginatedResponse(t *testing.T) {
	t.Parallel()

	params := PaginationParams{Page: 1, PageSize: 10}
	result := EmptyPaginatedResponse[string](params)
	
	assert.Empty(t, result.Data)
	assert.True(t, result.Success)
	assert.Equal(t, 1, result.Pagination.Page)
	assert.Equal(t, 10, result.Pagination.PageSize)
	assert.Equal(t, 0, result.Pagination.Total)
	assert.Equal(t, 0, result.Pagination.TotalPages)
	assert.False(t, result.Pagination.HasNext)
	assert.False(t, result.Pagination.HasPrevious)
}

func TestPaginationParams_Validate(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name      string
		params    PaginationParams
		expectErr bool
		errField  string
	}{
		{
			name:      "valid parameters",
			params:    PaginationParams{Page: 1, PageSize: 10},
			expectErr: false,
		},
		{
			name:      "valid maximum page size",
			params:    PaginationParams{Page: 1, PageSize: 100},
			expectErr: false,
		},
		{
			name:      "invalid page - zero",
			params:    PaginationParams{Page: 0, PageSize: 10},
			expectErr: true,
			errField:  "page",
		},
		{
			name:      "invalid page - negative",
			params:    PaginationParams{Page: -1, PageSize: 10},
			expectErr: true,
			errField:  "page",
		},
		{
			name:      "invalid page size - zero",
			params:    PaginationParams{Page: 1, PageSize: 0},
			expectErr: true,
			errField:  "page_size",
		},
		{
			name:      "invalid page size - negative",
			params:    PaginationParams{Page: 1, PageSize: -1},
			expectErr: true,
			errField:  "page_size",
		},
		{
			name:      "invalid page size - too large",
			params:    PaginationParams{Page: 1, PageSize: 101},
			expectErr: true,
			errField:  "page_size",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			err := tt.params.Validate()
			
			if tt.expectErr {
				require.Error(t, err)
				var validationErr *ValidationError
				require.ErrorAs(t, err, &validationErr)
				assert.Equal(t, tt.errField, validationErr.Field)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

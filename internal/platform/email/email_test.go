package email

import (
	"context"
	"log/slog"
	"os"
	"testing"

	"github.com/koopa0/pms-api-v2/internal/platform/config"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNew(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name      string
		config    *config.EmailConfig
		expectErr bool
		errMsg    string
	}{
		{
			name: "valid config",
			config: &config.EmailConfig{
				Enabled:  true,
				Host:     "smtp.gmail.com",
				Port:     587,
				Username: "<EMAIL>",
				Password: "password",
				From:     "<EMAIL>",
			},
			expectErr: false,
		},
		{
			name: "disabled email service",
			config: &config.EmailConfig{
				Enabled: false,
			},
			expectErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.<PERSON>()
			
			logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
				Level: slog.LevelError, // Reduce noise in tests
			}))

			service, err := New(tt.config, logger)

			if tt.expectErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
				assert.Nil(t, service)
			} else {
				require.NoError(t, err)
				assert.NotNil(t, service)

				assert.Equal(t, tt.config.Enabled, service.config.Enabled)
				assert.Equal(t, tt.config.Host, service.config.Host)
				assert.Equal(t, tt.config.Port, service.config.Port)
				assert.Equal(t, tt.config.Username, service.config.Username)
				assert.Equal(t, tt.config.Password, service.config.Password)
				assert.Equal(t, tt.config.From, service.config.From)

				// Check that templates are loaded
				assert.NotEmpty(t, service.templates)
			}
		})
	}
}

func TestService_SendEmail_DisabledService(t *testing.T) {
	t.Parallel()

	config := &config.EmailConfig{
		Enabled: false,
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	require.NoError(t, err)

	ctx := context.Background()
	data := EmailData{
		Username:    "testuser",
		CompanyName: "Test Company",
	}

	// Should not return error for disabled service
	err = service.SendRegistrationConfirmation(ctx, "<EMAIL>", data)
	assert.NoError(t, err)
}

func TestService_SendRegistrationConfirmation(t *testing.T) {
	t.Parallel()

	config := &config.EmailConfig{
		Enabled:  true,
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "<EMAIL>",
		Password: "password",
		From:     "<EMAIL>",
	}

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	service, err := New(config, logger)
	require.NoError(t, err)

	ctx := context.Background()

	tests := []struct {
		name      string
		to        string
		data      EmailData
		expectErr bool
	}{
		{
			name: "valid registration data",
			to:   "<EMAIL>",
			data: EmailData{
				Username:    "testuser",
				CompanyName: "Test Company",
			},
			expectErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := service.SendRegistrationConfirmation(ctx, tt.to, tt.data)

			if tt.expectErr {
				require.Error(t, err)
			} else {
				// Note: This will fail in actual SMTP sending, but validates the template and data
				// In a real test environment, you would mock the SMTP client
				if err != nil {
					// Allow SMTP connection errors in tests
					assert.Contains(t, err.Error(), "dial tcp")
				}
			}
		})
	}
}

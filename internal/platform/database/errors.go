package database

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"github.com/jackc/pgerrcode"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
)

// ErrorType represents the type of database error
type ErrorType int

const (
	// ErrorTypeUnknown represents an unknown error
	ErrorTypeUnknown ErrorType = iota
	// ErrorTypeNotFound represents a record not found error
	ErrorTypeNotFound
	// ErrorTypeDuplicate represents a duplicate key/unique violation error
	ErrorTypeDuplicate
	// ErrorTypeForeignKey represents a foreign key violation error
	ErrorTypeForeignKey
	// ErrorTypeCheckConstraint represents a check constraint violation error
	ErrorTypeCheckConstraint
	// ErrorTypeNotNull represents a not null violation error
	ErrorTypeNotNull
	// ErrorTypeInvalidInput represents invalid input/data format error
	ErrorTypeInvalidInput
	// ErrorTypeTimeout represents a query timeout error
	ErrorTypeTimeout
	// ErrorTypeConnectionFailed represents a connection failure
	ErrorTypeConnectionFailed
	// ErrorTypeTooManyConnections represents too many connections error
	ErrorTypeTooManyConnections
	// ErrorTypePermissionDenied represents a permission denied error
	ErrorTypePermissionDenied
	// ErrorTypeDeadlock represents a deadlock detected error
	ErrorTypeDeadlock
	// ErrorTypeLockTimeout represents a lock timeout error
	ErrorTypeLockTimeout
	// ErrorTypeSerializationFailure represents a serialization failure in transaction
	ErrorTypeSerializationFailure
)

// DBError represents a wrapped database error with additional context
type DBError struct {
	Type        ErrorType
	Code        string
	Message     string
	Detail      string
	Hint        string
	Table       string
	Column      string
	Constraint  string
	OriginalErr error
}

// Error implements the error interface
func (e *DBError) Error() string {
	return e.Message
}

// Unwrap returns the original error
func (e *DBError) Unwrap() error {
	return e.OriginalErr
}

// Is checks if the error is of a specific type
func (e *DBError) Is(target error) bool {
	if target == nil {
		return false
	}

	// Check if target is a DBError with the same type
	if targetDB, ok := target.(*DBError); ok {
		return e.Type == targetDB.Type
	}

	// Check against the original error
	return errors.Is(e.OriginalErr, target)
}

// WrapError wraps a database error with additional context and type information
func WrapError(err error, operation string) error {
	if err == nil {
		return nil
	}

	dbErr := &DBError{
		Type:        ErrorTypeUnknown,
		OriginalErr: err,
		Message:     fmt.Sprintf("%s failed", operation),
	}

	// Check for pgx specific errors
	if errors.Is(err, pgx.ErrNoRows) || errors.Is(err, sql.ErrNoRows) {
		dbErr.Type = ErrorTypeNotFound
		dbErr.Message = fmt.Sprintf("%s: record not found", operation)
		return dbErr
	}

	// Check for context errors
	if errors.Is(err, context.DeadlineExceeded) {
		dbErr.Type = ErrorTypeTimeout
		dbErr.Message = fmt.Sprintf("%s: operation timed out", operation)
		return dbErr
	}

	if errors.Is(err, context.Canceled) {
		dbErr.Type = ErrorTypeTimeout
		dbErr.Message = fmt.Sprintf("%s: operation was canceled", operation)
		return dbErr
	}

	// Check for PostgreSQL specific errors
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		dbErr.Code = pgErr.Code
		dbErr.Detail = pgErr.Detail
		dbErr.Hint = pgErr.Hint
		dbErr.Table = pgErr.TableName
		dbErr.Column = pgErr.ColumnName
		dbErr.Constraint = pgErr.ConstraintName

		// Map PostgreSQL error codes to error types using pgerrcode constants
		switch pgErr.Code {
		case pgerrcode.UniqueViolation:
			dbErr.Type = ErrorTypeDuplicate
			dbErr.Message = fmt.Sprintf("%s: duplicate key violation", operation)
			if pgErr.Detail != "" {
				dbErr.Message = fmt.Sprintf("%s: %s", dbErr.Message, extractDuplicateInfo(pgErr.Detail))
			}

		case pgerrcode.ForeignKeyViolation:
			dbErr.Type = ErrorTypeForeignKey
			dbErr.Message = fmt.Sprintf("%s: foreign key constraint violation", operation)
			if pgErr.ConstraintName != "" {
				dbErr.Message = fmt.Sprintf("%s on constraint '%s'", dbErr.Message, pgErr.ConstraintName)
			}

		case pgerrcode.NotNullViolation:
			dbErr.Type = ErrorTypeNotNull
			dbErr.Message = fmt.Sprintf("%s: null value not allowed", operation)
			if pgErr.ColumnName != "" {
				dbErr.Message = fmt.Sprintf("%s in column '%s'", dbErr.Message, pgErr.ColumnName)
			}

		case pgerrcode.CheckViolation:
			dbErr.Type = ErrorTypeCheckConstraint
			dbErr.Message = fmt.Sprintf("%s: check constraint violation", operation)
			if pgErr.ConstraintName != "" {
				dbErr.Message = fmt.Sprintf("%s on constraint '%s'", dbErr.Message, pgErr.ConstraintName)
			}

		case pgerrcode.InvalidTextRepresentation,
			pgerrcode.InvalidParameterValue,
			"22007", // invalid_datetime_format
			"22003", // numeric_value_out_of_range
			"22001": // string_data_right_truncation
			dbErr.Type = ErrorTypeInvalidInput
			dbErr.Message = fmt.Sprintf("%s: invalid input format or value", operation)

		case pgerrcode.QueryCanceled:
			dbErr.Type = ErrorTypeTimeout
			dbErr.Message = fmt.Sprintf("%s: query was canceled", operation)

		case pgerrcode.TooManyConnections:
			dbErr.Type = ErrorTypeTooManyConnections
			dbErr.Message = fmt.Sprintf("%s: too many database connections", operation)

		case pgerrcode.InsufficientPrivilege:
			dbErr.Type = ErrorTypePermissionDenied
			dbErr.Message = fmt.Sprintf("%s: permission denied", operation)

		case pgerrcode.DeadlockDetected:
			dbErr.Type = ErrorTypeDeadlock
			dbErr.Message = fmt.Sprintf("%s: deadlock detected", operation)

		case pgerrcode.LockNotAvailable:
			dbErr.Type = ErrorTypeLockTimeout
			dbErr.Message = fmt.Sprintf("%s: lock timeout", operation)

		case pgerrcode.SerializationFailure:
			dbErr.Type = ErrorTypeSerializationFailure
			dbErr.Message = fmt.Sprintf("%s: serialization failure, please retry", operation)

		case pgerrcode.ConnectionException,
			pgerrcode.ConnectionDoesNotExist,
			pgerrcode.ConnectionFailure,
			pgerrcode.SQLClientUnableToEstablishSQLConnection:
			dbErr.Type = ErrorTypeConnectionFailed
			dbErr.Message = fmt.Sprintf("%s: database connection failed", operation)

		default:
			// For any other PostgreSQL error
			dbErr.Message = fmt.Sprintf("%s: database error (code: %s)", operation, pgErr.Code)
		}

		// Add PostgreSQL message if available and not redundant
		if pgErr.Message != "" && !strings.Contains(dbErr.Message, pgErr.Message) {
			dbErr.Message = fmt.Sprintf("%s - %s", dbErr.Message, pgErr.Message)
		}

		return dbErr
	}

	// For non-PostgreSQL errors, try to determine type from error message
	errMsg := strings.ToLower(err.Error())
	switch {
	case strings.Contains(errMsg, "connection refused"):
		dbErr.Type = ErrorTypeConnectionFailed
		dbErr.Message = fmt.Sprintf("%s: unable to connect to database", operation)
	case strings.Contains(errMsg, "timeout"):
		dbErr.Type = ErrorTypeTimeout
		dbErr.Message = fmt.Sprintf("%s: operation timed out", operation)
	case strings.Contains(errMsg, "permission denied"):
		dbErr.Type = ErrorTypePermissionDenied
		dbErr.Message = fmt.Sprintf("%s: permission denied", operation)
	default:
		dbErr.Message = fmt.Sprintf("%s: %v", operation, err)
	}

	return dbErr
}

// extractDuplicateInfo extracts field information from PostgreSQL duplicate key error detail
func extractDuplicateInfo(detail string) string {
	// Example: "Key (username)=(test) already exists."
	if strings.Contains(detail, "Key (") {
		start := strings.Index(detail, "(") + 1
		end := strings.Index(detail, ")")
		if start > 0 && end > start {
			field := detail[start:end]
			valueStart := strings.Index(detail, "=(") + 2
			valueEnd := strings.Index(detail[valueStart:], ")")
			if valueStart > 1 && valueEnd > 0 {
				value := detail[valueStart : valueStart+valueEnd]
				return fmt.Sprintf("duplicate value '%s' for field '%s'", value, field)
			}
			return fmt.Sprintf("duplicate value for field '%s'", field)
		}
	}
	return "duplicate value"
}

// IsNotFound checks if the error is a not found error
func IsNotFound(err error) bool {
	var dbErr *DBError
	if errors.As(err, &dbErr) {
		return dbErr.Type == ErrorTypeNotFound
	}
	return errors.Is(err, pgx.ErrNoRows) || errors.Is(err, sql.ErrNoRows)
}

// IsDuplicate checks if the error is a duplicate key error
func IsDuplicate(err error) bool {
	var dbErr *DBError
	if errors.As(err, &dbErr) {
		return dbErr.Type == ErrorTypeDuplicate
	}
	return false
}

// IsForeignKeyViolation checks if the error is a foreign key violation
func IsForeignKeyViolation(err error) bool {
	var dbErr *DBError
	if errors.As(err, &dbErr) {
		return dbErr.Type == ErrorTypeForeignKey
	}
	return false
}

// IsTimeout checks if the error is a timeout error
func IsTimeout(err error) bool {
	var dbErr *DBError
	if errors.As(err, &dbErr) {
		return dbErr.Type == ErrorTypeTimeout
	}
	return errors.Is(err, context.DeadlineExceeded) || errors.Is(err, context.Canceled)
}

// IsConnectionError checks if the error is a connection error
func IsConnectionError(err error) bool {
	var dbErr *DBError
	if errors.As(err, &dbErr) {
		return dbErr.Type == ErrorTypeConnectionFailed || dbErr.Type == ErrorTypeTooManyConnections
	}
	return false
}

// IsRetryable checks if the error is retryable
func IsRetryable(err error) bool {
	var dbErr *DBError
	if errors.As(err, &dbErr) {
		switch dbErr.Type {
		case ErrorTypeDeadlock,
			ErrorTypeLockTimeout,
			ErrorTypeSerializationFailure,
			ErrorTypeConnectionFailed,
			ErrorTypeTooManyConnections:
			return true
		default:
			return false
		}
	}
	return false
}

// GetConstraintName extracts the constraint name from the error if available
func GetConstraintName(err error) string {
	var dbErr *DBError
	if errors.As(err, &dbErr) {
		return dbErr.Constraint
	}
	return ""
}

// GetColumnName extracts the column name from the error if available
func GetColumnName(err error) string {
	var dbErr *DBError
	if errors.As(err, &dbErr) {
		return dbErr.Column
	}
	return ""
}

// GetErrorCode extracts the PostgreSQL error code if available
func GetErrorCode(err error) string {
	var dbErr *DBError
	if errors.As(err, &dbErr) {
		return dbErr.Code
	}
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		return pgErr.Code
	}
	return ""
}

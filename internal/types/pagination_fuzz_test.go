package types

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// FuzzNewPaginationParams tests NewPaginationParams with random inputs
func FuzzNewPaginationParams(f *testing.F) {
	// Add seed corpus
	f.Add(1, 10)
	f.Add(0, 20)
	f.Add(-1, -1)
	f.Add(100, 200)
	f.Add(1, 1)

	f.Fuzz(func(t *testing.T, page, pageSize int) {
		result := NewPaginationParams(page, pageSize)
		
		// Property: page should always be at least 1
		assert.GreaterOrEqual(t, result.Page, 1)
		
		// Property: page size should be between 1 and 100
		assert.GreaterOrEqual(t, result.PageSize, 1)
		assert.LessOrEqual(t, result.PageSize, 100)
		
		// Property: function should not panic
		// Property: result should be deterministic
		result2 := NewPaginationParams(page, pageSize)
		assert.Equal(t, result, result2)
	})
}

// FuzzPaginationParams_Offset tests Offset calculation with random inputs
func FuzzPaginationParams_Offset(f *testing.F) {
	// Add seed corpus
	f.Add(1, 10)
	f.Add(2, 20)
	f.Add(100, 50)
	f.Add(1, 1)

	f.Fuzz(func(t *testing.T, page, pageSize int) {
		// Ensure valid inputs for this test
		if page < 1 {
			page = 1
		}
		if pageSize < 1 {
			pageSize = 1
		}
		if pageSize > 100 {
			pageSize = 100
		}

		params := PaginationParams{Page: page, PageSize: pageSize}
		offset := params.Offset()
		
		// Property: offset should be non-negative
		assert.GreaterOrEqual(t, offset, 0)
		
		// Property: offset should be (page-1) * pageSize
		expected := (page - 1) * pageSize
		assert.Equal(t, expected, offset)
		
		// Property: function should not panic
		// Property: result should be deterministic
		offset2 := params.Offset()
		assert.Equal(t, offset, offset2)
	})
}

// FuzzPaginationParams_Validate tests validation with random inputs
func FuzzPaginationParams_Validate(f *testing.F) {
	// Add seed corpus
	f.Add(1, 10)
	f.Add(0, 10)
	f.Add(1, 0)
	f.Add(-1, -1)
	f.Add(1, 101)

	f.Fuzz(func(t *testing.T, page, pageSize int) {
		params := PaginationParams{Page: page, PageSize: pageSize}
		err := params.Validate()
		
		// Property: validation should be consistent with rules
		if page < 1 || pageSize < 1 || pageSize > 100 {
			assert.Error(t, err)
		} else {
			assert.NoError(t, err)
		}
		
		// Property: function should not panic
		// Property: result should be deterministic
		err2 := params.Validate()
		if err == nil {
			assert.NoError(t, err2)
		} else {
			assert.Error(t, err2)
		}
	})
}

// FuzzCalculatePages tests page calculation with random inputs
func FuzzCalculatePages(f *testing.F) {
	// Add seed corpus
	f.Add(10, 5)
	f.Add(0, 10)
	f.Add(25, 10)
	f.Add(100, 0)
	f.Add(-1, 10)

	f.Fuzz(func(t *testing.T, total, pageSize int) {
		pages, hasMore := CalculatePages(total, pageSize)
		
		// Property: pages should be non-negative
		assert.GreaterOrEqual(t, pages, 0)
		
		// Property: if pageSize <= 0, pages should be 0
		if pageSize <= 0 {
			assert.Equal(t, 0, pages)
			assert.False(t, hasMore)
		}
		
		// Property: if total <= 0, pages should be 0
		if total <= 0 {
			assert.Equal(t, 0, pages)
			assert.False(t, hasMore)
		}
		
		// Property: hasMore should be true if total > pageSize (when both positive)
		if total > 0 && pageSize > 0 {
			if total > pageSize {
				assert.True(t, hasMore)
			} else {
				assert.False(t, hasMore)
			}
		}
		
		// Property: function should not panic
		// Property: result should be deterministic
		pages2, hasMore2 := CalculatePages(total, pageSize)
		assert.Equal(t, pages, pages2)
		assert.Equal(t, hasMore, hasMore2)
	})
}

// FuzzPaginationParams_IsValidPage tests page validation with random inputs
func FuzzPaginationParams_IsValidPage(f *testing.F) {
	// Add seed corpus
	f.Add(1, 10, 25)
	f.Add(0, 10, 25)
	f.Add(5, 10, 25)
	f.Add(1, 10, 0)
	f.Add(-1, 10, 25)

	f.Fuzz(func(t *testing.T, page, pageSize, total int) {
		// Ensure pageSize is positive for meaningful test
		if pageSize <= 0 {
			pageSize = 10
		}

		params := PaginationParams{Page: page, PageSize: pageSize}
		isValid := params.IsValidPage(total)
		
		// Property: page < 1 should always be invalid
		if page < 1 {
			assert.False(t, isValid)
		}
		
		// Property: if total <= 0, should be invalid
		if total <= 0 {
			assert.False(t, isValid)
		}
		
		// Property: function should not panic
		// Property: result should be deterministic
		isValid2 := params.IsValidPage(total)
		assert.Equal(t, isValid, isValid2)
	})
}

// FuzzNewPaginatedResponse tests paginated response creation with random inputs
func FuzzNewPaginatedResponse(f *testing.F) {
	// Add seed corpus
	f.Add(5, 1, 10, 25)
	f.Add(0, 1, 10, 0)
	f.Add(10, 2, 10, 25)

	f.Fuzz(func(t *testing.T, dataLen, page, pageSize, total int) {
		// Ensure valid pagination params
		if page < 1 {
			page = 1
		}
		if pageSize < 1 {
			pageSize = 10
		}
		if pageSize > 100 {
			pageSize = 100
		}
		if dataLen < 0 {
			dataLen = 0
		}
		if total < 0 {
			total = 0
		}

		// Create test data
		data := make([]string, dataLen)
		for i := range data {
			data[i] = "item"
		}

		params := PaginationParams{Page: page, PageSize: pageSize}
		response := NewPaginatedResponse(data, params, total)
		
		// Property: response should always be successful
		assert.True(t, response.Success)
		
		// Property: data should match input
		assert.Equal(t, data, response.Data)
		
		// Property: pagination metadata should match params
		assert.Equal(t, page, response.Pagination.Page)
		assert.Equal(t, pageSize, response.Pagination.PageSize)
		assert.Equal(t, total, response.Pagination.Total)
		
		// Property: total pages should be calculated correctly
		expectedPages := 0
		if pageSize > 0 && total > 0 {
			expectedPages = int((float64(total) + float64(pageSize) - 1) / float64(pageSize))
		}
		assert.Equal(t, expectedPages, response.Pagination.TotalPages)
		
		// Property: function should not panic
		// Property: result should be deterministic for same inputs
		response2 := NewPaginatedResponse(data, params, total)
		assert.Equal(t, response.Success, response2.Success)
		assert.Equal(t, response.Pagination, response2.Pagination)
	})
}

// FuzzPaginationParams_ApplyDefaults tests default application with random inputs
func FuzzPaginationParams_ApplyDefaults(f *testing.F) {
	// Add seed corpus
	f.Add(1, 10, 20, 100)
	f.Add(0, 0, 15, 50)
	f.Add(-1, -1, 25, 75)
	f.Add(5, 200, 30, 80)

	f.Fuzz(func(t *testing.T, page, pageSize, defaultSize, maxSize int) {
		// Ensure config values are reasonable
		if defaultSize < 1 {
			defaultSize = 20
		}
		if maxSize < defaultSize {
			maxSize = defaultSize
		}

		config := PaginationConfig{
			DefaultPageSize: defaultSize,
			MaxPageSize:     maxSize,
		}

		params := PaginationParams{Page: page, PageSize: pageSize}
		originalParams := params
		params.ApplyDefaults(config)
		
		// Property: page should be at least 1
		assert.GreaterOrEqual(t, params.Page, 1)
		
		// Property: page size should be between 1 and maxSize
		assert.GreaterOrEqual(t, params.PageSize, 1)
		assert.LessOrEqual(t, params.PageSize, maxSize)
		
		// Property: if original page was valid, it should be unchanged
		if originalParams.Page >= 1 {
			assert.Equal(t, originalParams.Page, params.Page)
		}
		
		// Property: if original pageSize was valid, it should be unchanged
		if originalParams.PageSize >= 1 && originalParams.PageSize <= maxSize {
			assert.Equal(t, originalParams.PageSize, params.PageSize)
		}
		
		// Property: function should not panic
		// Property: result should be deterministic
		params2 := originalParams
		params2.ApplyDefaults(config)
		assert.Equal(t, params, params2)
	})
}
